# 前端API路径更新说明

## 问题说明

由于后端 `SysUserController` 中已经存在 `/sys/user/register` 接口，为了避免路径冲突，我们将新的认证接口路径从 `/sys/user/*` 修改为 `/sys/auth/*`。

## 需要更新的前端文件

### 文件位置
- `taiyi_portal/static/js/auth-components.js`

### 需要修改的API路径

#### 1. 登录接口
**修改前：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/login`, {
```

**修改后：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/auth/login`, {
```

#### 2. 注册接口
**修改前：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/register`, {
```

**修改后：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/auth/register`, {
```

#### 3. 发送重置密码验证码接口
**修改前：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/sendResetCode`, {
```

**修改后：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/auth/sendResetCode`, {
```

#### 4. 重置密码接口
**修改前：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/resetPassword`, {
```

**修改后：**
```javascript
const response = await fetch(`${jeeApi}/jeecg-boot/sys/auth/resetPassword`, {
```

## 完整的API路径列表

| 功能 | 新路径 | 方法 |
|------|--------|------|
| 登录 | `/sys/auth/login` | POST |
| 注册 | `/sys/auth/register` | POST |
| 发送重置验证码 | `/sys/auth/sendResetCode` | POST |
| 重置密码 | `/sys/auth/resetPassword` | POST |

## 修改步骤

1. 打开 `taiyi_portal/static/js/auth-components.js` 文件
2. 使用查找替换功能：
   - 查找：`/sys/user/`
   - 替换为：`/sys/auth/`
3. 确保所有4个接口路径都已更新
4. 保存文件

## 验证方法

修改完成后，可以通过以下方式验证：

1. 启动后端服务
2. 打开前端测试页面
3. 测试登录、注册、重置密码功能
4. 检查浏览器开发者工具的网络请求，确认请求路径为 `/sys/auth/*`

## 注意事项

- 确保后端服务已重启
- 清除浏览器缓存以避免旧的JS文件缓存
- 如果有其他页面也使用了这些API，也需要同步更新
