package org.jeecg.modules.system.vo.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 租户记录统计信息VO
 * @author: jeecg-boot
 */
@Data
@ApiModel(value = "TenantRecordStatisticsVO", description = "租户记录统计信息VO")
public class TenantRecordStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 历史浏览记录数量
     */
    @ApiModelProperty(value = "历史浏览记录数量")
    private Integer browseCount;

    /**
     * 收藏记录数量
     */
    @ApiModelProperty(value = "收藏记录数量")
    private Integer favoriteCount;

    /**
     * 点赞记录数量
     */
    @ApiModelProperty(value = "点赞记录数量")
    private Integer likeCount;

    /**
     * 分享记录数量
     */
    @ApiModelProperty(value = "分享记录数量")
    private Integer shareCount;

    /**
     * 构造函数
     */
    public TenantRecordStatisticsVO() {
        this.browseCount = 0;
        this.favoriteCount = 0;
        this.likeCount = 0;
        this.shareCount = 0;
    }

    /**
     * 构造函数
     * @param browseCount 浏览数
     * @param favoriteCount 收藏数
     * @param likeCount 点赞数
     * @param shareCount 分享数
     */
    public TenantRecordStatisticsVO(Integer browseCount, Integer favoriteCount, Integer likeCount, Integer shareCount) {
        this.browseCount = browseCount != null ? browseCount : 0;
        this.favoriteCount = favoriteCount != null ? favoriteCount : 0;
        this.likeCount = likeCount != null ? likeCount : 0;
        this.shareCount = shareCount != null ? shareCount : 0;
    }

    /**
     * 转换为Integer数组
     * @return [browseCount, favoriteCount, likeCount, shareCount]
     */
    public Integer[] toArray() {
        return new Integer[]{browseCount, favoriteCount, likeCount, shareCount};
    }
}
