package org.jeecg.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.vo.LoginVo;
import org.jeecg.modules.system.vo.RegisterVo;
import org.jeecg.modules.system.vo.ResetPasswordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.constant.CommonConstant;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 用户认证控制器
 */
@Api(tags = "用户认证")
@RestController
@RequestMapping("/sys/auth")
@Slf4j
public class SysUserAuthController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 用户名密码登录（无验证码）
     */
    @ApiOperation("用户名密码登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody @Valid LoginVo loginVo) {
        try {
            // 查询用户
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", loginVo.getUsername());
            queryWrapper.eq("status", 1); // 正常状态
            queryWrapper.eq("del_flag", 0); // 未删除
            SysUser user = sysUserService.getOne(queryWrapper);

            if (user == null) {
                return Result.error("用户名不存在");
            }

            // 验证密码
            String inputPassword = PasswordUtil.encrypt(loginVo.getUsername(), loginVo.getPassword(), user.getSalt());
            if (!inputPassword.equals(user.getPassword())) {
                return Result.error("密码错误");
            }

            // 生成token
            String token = JwtUtil.sign(user.getUsername(), user.getPassword());

            // 设置token缓存有效时间（关键步骤：将token存储到Redis缓存中）
            redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("realname", user.getRealname());
            userInfo.put("email", user.getEmail());
            userInfo.put("phone", user.getPhone());
            result.put("userInfo", userInfo);

            log.info("用户 {} 登录成功", user.getUsername());
            return Result.OK("登录成功", result);

        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户名注册
     */
    @ApiOperation("用户名注册")
    @PostMapping("/register")
    public Result<String> register(@RequestBody @Valid RegisterVo registerVo) {
        try {
            // 检查用户名是否已存在
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", registerVo.getUsername());
            queryWrapper.eq("del_flag", 0);
            SysUser existUser = sysUserService.getOne(queryWrapper);
            if (existUser != null) {
                return Result.error("该用户名已被注册");
            }

            // 创建用户
            SysUser user = new SysUser();
            user.setUsername(registerVo.getUsername());
            user.setRealname(registerVo.getUsername()); // 默认真实姓名为用户名

            // 加密密码
            String salt = oConvertUtils.randomGen(8);
            String encryptedPassword = PasswordUtil.encrypt(registerVo.getUsername(), registerVo.getPassword(), salt);
            user.setPassword(encryptedPassword);
            user.setSalt(salt);

            user.setStatus(1); // 正常状态
            user.setDelFlag(0); // 未删除

            // 保存用户
            sysUserService.save(user);

            log.info("用户 {} 注册成功", registerVo.getUsername());
            return Result.OK("注册成功");

        } catch (Exception e) {
            log.error("注册失败", e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 简化注册接口（只需用户名和密码，设置user_identity为2）
     */
    @ApiOperation("简化注册接口")
    @PostMapping("/simpleRegister")
    public Result<String> simpleRegister(@RequestBody Map<String, String> params) {
        try {
            String username = params.get("username");
            String password = params.get("password");

            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }

            // 检查用户名是否已存在
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username.trim());
            queryWrapper.eq("del_flag", 0);
            SysUser existUser = sysUserService.getOne(queryWrapper);
            if (existUser != null) {
                return Result.error("该用户名已被注册");
            }

            // 创建用户
            SysUser user = new SysUser();
            user.setUsername(username.trim());
            user.setRealname(username.trim()); // 默认真实姓名为用户名
            user.setCreateTime(new Date()); // 设置创建时间

            // 加密密码
            String salt = oConvertUtils.randomGen(8);
            String encryptedPassword = PasswordUtil.encrypt(username.trim(), password, salt);
            user.setPassword(encryptedPassword);
            user.setSalt(salt);

            // 设置用户状态和标识
            user.setStatus(CommonConstant.USER_UNFREEZE); // 正常状态
            user.setDelFlag(CommonConstant.DEL_FLAG_0); // 未删除
            user.setUserIdentity(CommonConstant.USER_IDENTITY_2); // 设置用户身份为2（上级）
            user.setActivitiSync(CommonConstant.ACT_SYNC_1); // 同步工作流引擎

            // 保存用户（使用addUserWithRole方法以确保事务完整性）
            sysUserService.addUserWithRole(user, ""); // 不分配角色

            log.info("用户 {} 简化注册成功，用户身份设置为2", username);
            return Result.OK("注册成功");

        } catch (Exception e) {
            log.error("简化注册失败", e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 发送重置密码验证码
     */
    @ApiOperation("发送重置密码验证码")
    @PostMapping("/sendResetCode")
    public Result<String> sendResetCode(@RequestBody Map<String, String> params) {
        try {
            String username = params.get("username");

            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }

            // 验证用户名是否存在
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username);
            queryWrapper.eq("del_flag", 0);
            SysUser user = sysUserService.getOne(queryWrapper);

            if (user == null) {
                return Result.error("用户名不存在");
            }

            // 生成6位验证码
            String code = String.format("%06d", new Random().nextInt(999999));

            // 存储到Redis，5分钟过期
            String redisKey = "reset_code:" + username;
            redisTemplate.opsForValue().set(redisKey, code, 5, TimeUnit.MINUTES);

            log.info("用户 {} 的重置密码验证码已生成: {}", username, code);
            return Result.OK("验证码已生成，验证码为：" + code + "（实际应用中应通过短信或其他方式发送）");

        } catch (Exception e) {
            log.error("发送重置密码验证码失败", e);
            return Result.error("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 重置密码
     */
    @ApiOperation("重置密码")
    @PostMapping("/resetPassword")
    public Result<String> resetPassword(@RequestBody @Valid ResetPasswordVo resetPasswordVo) {
        try {
            // 验证验证码
            String redisKey = "reset_code:" + resetPasswordVo.getUsername();
            String storedCode = redisTemplate.opsForValue().get(redisKey);
            if (storedCode == null) {
                return Result.error("验证码已过期，请重新获取");
            }
            if (!storedCode.equals(resetPasswordVo.getCode())) {
                return Result.error("验证码错误");
            }

            // 查询用户
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", resetPasswordVo.getUsername());
            queryWrapper.eq("del_flag", 0);
            SysUser user = sysUserService.getOne(queryWrapper);

            if (user == null) {
                return Result.error("用户名不存在");
            }

            // 更新密码
            String salt = oConvertUtils.randomGen(8);
            String encryptedPassword = PasswordUtil.encrypt(user.getUsername(), resetPasswordVo.getNewPassword(), salt);
            user.setPassword(encryptedPassword);
            user.setSalt(salt);

            // 保存用户
            sysUserService.updateById(user);

            // 删除验证码
            redisTemplate.delete(redisKey);

            log.info("用户 {} 重置密码成功", user.getUsername());
            return Result.OK("密码重置成功");

        } catch (Exception e) {
            log.error("重置密码失败", e);
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }
}
