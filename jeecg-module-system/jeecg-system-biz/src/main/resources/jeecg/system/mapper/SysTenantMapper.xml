<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysTenantMapper">

    <!-- 租户记录统计信息结果映射 -->
    <resultMap id="TenantRecordStatisticsMap" type="org.jeecg.modules.system.vo.tenant.TenantRecordStatisticsVO">
        <result column="browse_count" property="browseCount" jdbcType="INTEGER"/>
        <result column="favorite_count" property="favoriteCount" jdbcType="INTEGER"/>
        <result column="like_count" property="likeCount" jdbcType="INTEGER"/>
        <result column="share_count" property="shareCount" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 根据内容ID获取统计信息 -->
    <select id="getRecordStatisticsByPid" parameterType="java.lang.String" resultMap="TenantRecordStatisticsMap">
        SELECT 
            COALESCE(SUM(CASE WHEN wrs.record_type = 1 THEN wrs.count ELSE 0 END), 0) AS browse_count,
            COALESCE(SUM(CASE WHEN wrs.record_type = 2 THEN wrs.count ELSE 0 END), 0) AS favorite_count,
            COALESCE(SUM(CASE WHEN wrs.record_type = 3 THEN wrs.count ELSE 0 END), 0) AS like_count,
            COALESCE(SUM(CASE WHEN wrs.record_type = 4 THEN wrs.count ELSE 0 END), 0) AS share_count
        FROM wh_record_statistics wrs
        WHERE wrs.content_id = #{pid}
          AND wrs.content_type = 3
    </select>

</mapper>
