SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change_v519`;
DELIMITER ;;

CREATE PROCEDURE schema_change_v519()
BEGIN
    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 创建用户门户记录表
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '#(database_name)'
        AND TABLE_NAME = 'wh_record'
    ) THEN
CREATE TABLE `#(database_name)`.`wh_record` (
                                                `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `tenant_id` INT(11) NOT NULL DEFAULT 0 COMMENT '租户ID',
                                                `record_type` TINYINT(1) NOT NULL COMMENT '记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录',
                                                `content_type` TINYINT(1) NOT NULL COMMENT '内容类型：1.保险产品 2.保险资讯 3.保险公司',
                                                `content_id` VARCHAR(50) NOT NULL COMMENT '内容ID',
                                                `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
                                                `link` VARCHAR(255) NOT NULL COMMENT '链接id',
                                                `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
                                                `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
                                                `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`),
                                                KEY `idx_tenant_id` (`tenant_id`),
                                                KEY `idx_user_id` (`user_id`),
                                                KEY `idx_record_type` (`record_type`),
                                                KEY `idx_content_type` (`content_type`),
                                                UNIQUE KEY `uk_user_record_content` (`user_id`, `record_type`, `content_type`, `content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户门户记录表';
END IF;

    -- 用户门户记录统计表
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '#(database_name)'
        AND TABLE_NAME = 'wh_record_statistics'
    ) THEN
CREATE TABLE `#(database_name)`.`wh_record_statistics` (
                                                           `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                           `count` INT(11) NOT NULL DEFAULT 0 COMMENT '数量',
                                                           `record_type` TINYINT(1) NOT NULL COMMENT '记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录',
                                                           `pid` VARCHAR(50) NOT NULL COMMENT '对应的内容ID',
                                                           PRIMARY KEY (`id`),
                                                           UNIQUE KEY `uk_record_type_pid` (`record_type`, `pid`) COMMENT '记录类型和内容ID唯一索引',
                                                           KEY `idx_record_type` (`record_type`) COMMENT '记录类型索引',
                                                           KEY `idx_pid` (`pid`) COMMENT '内容ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户门户记录统计表';
END IF;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;
END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change_v519();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change_v519`;

SET FOREIGN_KEY_CHECKS = 1;