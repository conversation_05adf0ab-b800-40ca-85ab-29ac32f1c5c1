SET FOREIGN_KEY_CHECKS = 0;

DROP PROCEDURE IF EXISTS `schema_change_v518`;
DELIMITER ;;

CREATE PROCEDURE schema_change_v518()
BEGIN
    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 修改 app_news 表的 tenant_id 字段默认值为 0
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = '#(database_name)'
        AND TABLE_NAME = 'click_auto_pre'
        AND COLUMN_NAME = 'amount'
    ) THEN
ALTER TABLE `#(database_name)`.`click_auto_pre`
    MODIFY COLUMN `amount` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '金额';


    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;
END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change_v518();

-- 删除存储过程
DROP PROCEDURE IF EXISTS `schema_change_v518`;

SET FOREIGN_KEY_CHECKS = 1;