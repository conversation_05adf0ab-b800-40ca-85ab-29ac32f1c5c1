# 简化认证功能说明

## 功能概述

简化后的用户认证系统，包括：

1. **用户名密码登录**（无验证码）
2. **用户名注册**（无邮箱验证）
3. **密码重置**（用户名+验证码，验证码直接返回）

## API接口

### 1. 用户名密码登录
```
POST /sys/auth/login
Content-Type: application/json

{
    "username": "用户名",
    "password": "密码"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "登录成功",
    "result": {
        "token": "jwt-token",
        "userInfo": {
            "id": "用户ID",
            "username": "用户名",
            "realname": "真实姓名",
            "email": "邮箱",
            "phone": "电话"
        }
    }
}
```

### 2. 用户名注册
```
POST /sys/auth/register
Content-Type: application/json

{
    "username": "用户名",
    "password": "密码"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "注册成功"
}
```

### 3. 发送重置密码验证码
```
POST /sys/auth/sendResetCode
Content-Type: application/json

{
    "username": "用户名"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "验证码已生成，验证码为：123456（实际应用中应通过短信或其他方式发送）"
}
```

### 4. 重置密码
```
POST /sys/auth/resetPassword
Content-Type: application/json

{
    "username": "用户名",
    "code": "验证码",
    "newPassword": "新密码"
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "密码重置成功"
}
```

## 技术实现

### 密码加密
- 使用 `PasswordUtil.encrypt(username, password, salt)` 加密
- 每个用户使用随机8位盐值
- 加密算法：PBEWithMD5AndDES

### 验证码管理
- 使用Redis存储验证码，5分钟过期
- 重置密码验证码key：`reset_code:{username}`
- 验证码直接在响应中返回（开发测试用，生产环境应通过短信等方式发送）

### 数据库表
使用现有的 `sys_user` 表：
- `username`：用户名（必填）
- `password`：加密后的密码
- `salt`：密码盐值
- `realname`：真实姓名（默认等于用户名）
- `status`：用户状态（1=正常）
- `del_flag`：删除标志（0=未删除）

## 业务流程

### 注册流程
1. 用户输入用户名和密码
2. 系统检查用户名是否已存在
3. 创建用户账号
4. 返回注册成功

### 登录流程
1. 用户输入用户名和密码
2. 系统验证用户名和密码
3. 生成JWT token
4. 返回token和用户信息

### 重置密码流程
1. 用户输入用户名
2. 系统验证用户名是否存在
3. 生成6位数字验证码并存储到Redis
4. 返回验证码（实际应用中应发送到用户手机或邮箱）
5. 用户输入验证码和新密码
6. 系统验证验证码
7. 更新用户密码

## 错误处理

### 常见错误码
- 用户名不存在
- 密码错误
- 用户名已被注册
- 验证码错误或过期

### 安全考虑
1. 密码使用盐值加密存储
2. 验证码有效期5分钟
3. JWT token用于会话管理
4. 所有密码相关操作都有日志记录

## 测试示例

### 注册测试
```bash
curl -X POST http://localhost:8080/jeecg-boot/sys/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

### 登录测试
```bash
curl -X POST http://localhost:8080/jeecg-boot/sys/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

### 重置密码测试
```bash
# 1. 获取验证码
curl -X POST http://localhost:8080/jeecg-boot/sys/auth/sendResetCode \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser"
  }'

# 2. 重置密码
curl -X POST http://localhost:8080/jeecg-boot/sys/auth/resetPassword \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "code": "123456",
    "newPassword": "newpassword"
  }'
```

## 注意事项

1. **验证码发送**：当前验证码直接在响应中返回，生产环境应集成短信服务
2. **安全性**：所有密码都经过加密存储，不可逆
3. **会话管理**：使用JWT token进行用户会话管理
4. **日志记录**：所有认证操作都有详细的日志记录
5. **数据验证**：所有输入参数都有完整的验证规则
