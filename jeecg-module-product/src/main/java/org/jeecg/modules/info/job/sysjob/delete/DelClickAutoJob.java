package org.jeecg.modules.info.job.sysjob.delete;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.entity.ClickAutoPre;
import org.jeecg.modules.corp.service.IClickAutoPreService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Slf4j
@Component
public class DelClickAutoJob implements Job {
    @Autowired
    private IClickAutoPreService preService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("开始执行删除三天前ClickAutoPre数据的定时任务");

        try {
            // 计算三天前的时间点
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
            Timestamp threeDaysAgoTimestamp = Timestamp.valueOf(threeDaysAgo);

            log.info("删除点击时间早于 {} 的ClickAutoPre数据", threeDaysAgoTimestamp);

            // 使用MyBatis-Plus的lambdaUpdate方法删除三天前的数据
            boolean result = preService.lambdaUpdate()
                    .lt(ClickAutoPre::getClickTime, threeDaysAgoTimestamp)
                    .remove();

            if (result) {
                log.info("成功删除三天前的ClickAutoPre数据");
            } else {
                log.info("没有找到需要删除的三天前ClickAutoPre数据");
            }

        } catch (Exception e) {
            log.error("删除三天前ClickAutoPre数据时发生异常", e);
            throw new JobExecutionException("删除三天前ClickAutoPre数据失败", e);
        }

        log.info("删除三天前ClickAutoPre数据的定时任务执行完成");
    }
}
