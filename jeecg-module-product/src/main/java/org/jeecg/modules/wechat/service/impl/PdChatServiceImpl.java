package org.jeecg.modules.wechat.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.wechat.config.PdChatConfig;
import org.jeecg.modules.wechat.dto.AdditionalMessageDto;
import org.jeecg.modules.wechat.dto.SendChatDto;
import org.jeecg.modules.wechat.dto.SendMessageDto;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.mapper.AppNewsMapper;
import org.jeecg.modules.wechat.mapper.PdChatMapper;
import org.jeecg.modules.wechat.service.CozeAuthService;
import org.jeecg.modules.wechat.service.IPdChatConfigService;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.jeecg.modules.wechat.vo.PdChatVO;
import org.jeecg.modules.wechat.vo.chat.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.jeecg.config.mybatis.TimeRestrictionUtil;
import org.jeecg.config.mybatis.TenantUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Arrays;

/**
 * @Description: 聊天记录
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class PdChatServiceImpl extends ServiceImpl<PdChatMapper, PdChat> implements IPdChatService {



    @Value("${coze.sendUrl}")
    private String sendUrl;

    @Value("${coze.listUrl}")
    private String listUrl;

    @Value("${coze.botId}")
    private String botId;


    @Value("${coze.workflowId}")
    private String workflowId;

    @Value("${coze.author}")
    private String author;

    @Resource
    private IPdChatConfigService pdChatConfigService;


    private static final int MAX_RETRY_COUNT = 5;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CozeAuthService cozeAuthService;


    @Autowired
    private IPdGuestUsersService pdGuestUsersService;
    @Autowired
    private TimeRestrictionUtil timeRestrictionUtil;


    @Override
    public ChatIdVO sendMessage(SendMessageDto dto) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");
        //log.info("报价租户,游客id:{},{}",tenantId,guestId);
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("未找到租户 id");
        }

        //1. 保存信息
        PdChat pdChat = new PdChat();
        pdChat.setUserId(dto.getUserId()).setSendTime(new Date())
                .setMessage(dto.getMessage())
                .setIpAddress(dto.getSendIp())
                .setSendType(0);
        this.save(pdChat);
        //2. 调用扣子
        SendChatDto chatDto = new SendChatDto();
        chatDto.setBotId(botId).setUserId(dto.getUserId()).setAutoSaveHistory(true);
        String shelfConfig = this.baseMapper.getConfig(tenantId);
        //log.info("shelfConfig:{}",shelfConfig);
        if (!StrUtil.isNotEmpty(shelfConfig) || shelfConfig.equals("1")) {
            //企业知识问答
            chatDto.setBotId("7438915765665398822");
        }

        AdditionalMessageDto messageDto = new AdditionalMessageDto();
        messageDto.setContentType("text").setContent(dto.getMessage()).setRole("user");
        chatDto.setAdditionalMessages(Collections.singletonList(messageDto));
        ChatIdVO chatIdVO ;

        try {
            CloseableHttpResponse closeableHttpResponse = sendChatMessage(chatDto);
            chatIdVO = handleChatResponse(chatDto, closeableHttpResponse);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        // 调用发送消息的方法
        return chatIdVO;

    }



    @Override
    public ReplyVO getTheMessage(ChatIdVO dto) {
        ReplyVO replyVO = new ReplyVO();
        ChatResponseVo messageList = getMessageList(dto.getId(), dto.getConversationId());
        messageList.getData().forEach(item->{
            if ("answer".equals(item.getType())){
                replyVO.setContent(item.getContent());
            }
        });
        if (StrUtil.isBlank(replyVO.getContent()) || StrUtil.isEmpty(replyVO.getContent())) {
            throw new RuntimeException("请再次轮询，消息还未返回");
        }
        PdChat pdChat = new PdChat();
        pdChat.setUserId(dto.getUserId())
                .setIpAddress("127.0.0.1")
                .setSendType(1).setSendTime(new Date()).setMessage(replyVO.getContent());
        this.save(pdChat);

        return replyVO;
    }

    @Override
    public String getAiMessage(String content, String botId) {
        //2. 调用扣子
        SendChatDto chatDto = new SendChatDto();
        //生成随机不重复字符串
        String userId = RandomUtil.randomString(16);
        chatDto.setBotId(botId).setUserId(userId).setAutoSaveHistory(true);
        AdditionalMessageDto messageDto = new AdditionalMessageDto();
        messageDto.setContentType("text").setContent(content).setRole("user");
        chatDto.setAdditionalMessages(Collections.singletonList(messageDto));
        ChatIdVO chatIdVO ;

        try {
            CloseableHttpResponse closeableHttpResponse = sendChatMessage(chatDto);
            chatIdVO = handleChatResponse(chatDto, closeableHttpResponse);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        if (Objects.isNull(chatIdVO)) {
            return null;
        }
        ReplyVO replyVO = pollReplyVO(chatIdVO);

        return replyVO.getContent();

    }

    @Override
    public String buttonRecord(String content, String botId) {
        //调用扣子
        SendChatDto chatDto = new SendChatDto();
        //生成随机不重复字符串
        Object sendCoze = null;
        String userId = "";
        if (sendCoze != null) {
            //log.info("使用同一对话");
            userId =  sendCoze.toString();
        }else {
            //log.info("生成对话");
            String userIds = RandomUtil.randomString(16);
            redisTemplate.opsForValue().set("sendCoze", userIds,1, TimeUnit.DAYS);
            userId = userIds;
            log.info("userId:"+userId);
        }

        chatDto.setBotId(botId).setUserId(userId).setAutoSaveHistory(true);
        AdditionalMessageDto messageDto = new AdditionalMessageDto();
        messageDto.setContentType("text").setContent(content).setRole("user");
        chatDto.setAdditionalMessages(Collections.singletonList(messageDto));
        ChatIdVO chatIdVO ;

        try {
            CloseableHttpResponse closeableHttpResponse = sendAiChatMessage(chatDto);
            chatIdVO = handleChatAiResponse(chatDto, closeableHttpResponse);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        if (Objects.isNull(chatIdVO)) {
            return null;
        }
        ReplyVO replyVO = pollReplyVO(chatIdVO);

        return replyVO.getContent();
    }

    private ChatIdVO handleChatAiResponse(SendChatDto chatDto, CloseableHttpResponse response) throws IOException, InterruptedException {
        ChatIdVO chatIdVO = new ChatIdVO();
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject res = JSONObject.parseObject(responseBody);
        JSONObject data = res.getJSONObject("data");

        if (ObjectUtil.isNotEmpty(data) && ObjectUtil.isNotEmpty(data.getString("id"))) {
            String chatId = data.getString("id");
            String conversationId = data.getString("conversation_id");
            chatIdVO.setConversationId(conversationId).setId(chatId);

            if (ObjectUtil.isEmpty(redisTemplate.opsForValue().get("chat_id_" + chatDto.getUserId()))) {
                redisTemplate.opsForValue().set("chat_id_" + chatDto.getUserId(), conversationId, 10, TimeUnit.MINUTES);
            }

            String status = data.getString("status");
            if ("in_progress".equals(status)) {
                log.info("对话正在处理中，开始轮询状态...");
                String content = chatDto.getAdditionalMessages().get(0).getContent();
                checkAiChatStatus(chatIdVO,content);
            } else if ("completed".equals(status)) {
                chatIdVO.setId(chatId).setConversationId(conversationId);
            }

            return chatIdVO;
        }
        return null; // 响应不成功
    }

    private void checkAiChatStatus(ChatIdVO chatIdVO, String content)  throws IOException, InterruptedException {
        // 检查 dto 中是否包含“人工”

        String checkUrl = "https://api.coze.cn/v3/chat/retrieve?chat_id=" + chatIdVO.getId() + "&conversation_id=" + chatIdVO.getConversationId();
        int retryCount = 0;
        String accessToken = null;
        try {
            accessToken = cozeAuthService.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        while (retryCount < MAX_RETRY_COUNT) {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet get = new HttpGet(checkUrl);
                get.setHeader("Authorization", "Bearer " + accessToken);
                get.setHeader("Content-Type", "application/json");

                try (CloseableHttpResponse response = httpClient.execute(get)) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject res = JSONObject.parseObject(responseBody);
                    JSONObject data = res.getJSONObject("data");

                    if (res.getIntValue("code") == 0 && ObjectUtil.isNotEmpty(data)) {
                        String status = data.getString("status");
                        if ("completed".equals(status)) {
                            System.out.println("对话已完成");
                            break;
                        } else {
                            System.out.println("对话未完成，当前状态为 " + status + "，等待 1.5 秒后重试...");
                            Thread.sleep(5000);
                            retryCount++;
                        }
                    }
                }
            }
        }
    }

    private CloseableHttpResponse sendAiChatMessage(SendChatDto chatDto) throws IOException{
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost post = new HttpPost(sendUrl);

        String accessToken = null;
        try {
            accessToken = cozeAuthService.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 从 Redis 获取 chat_id
        Object chatId = redisTemplate.opsForValue().get("chat_id_" + chatDto.getUserId());
        if (ObjectUtil.isNotEmpty(chatId)) {
            //log.info("使用 Redis 获取的 chat_id");
            String conversationId = chatId.toString();
            post.setURI(URI.create(sendUrl + "?conversation_id=" + conversationId));
        }

        //post.setHeader("Authorization", accessToken); // 确保使用了正确的令牌
        post.setHeader("Authorization", "Bearer " + accessToken);
        post.setHeader("Content-Type", "application/json");

        // 创建符合目标格式的 JSON 对象
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode json = objectMapper.createObjectNode();
        json.put("bot_id", chatDto.getBotId());
        json.put("user_id", chatDto.getUserId());
        json.put("auto_save_history", chatDto.isAutoSaveHistory());
        json.put("stream", chatDto.isStream());

        ArrayNode additionalMessages = objectMapper.createArrayNode();
        for (AdditionalMessageDto msg : chatDto.getAdditionalMessages()) {
            ObjectNode messageJson = objectMapper.createObjectNode();
            messageJson.put("role", msg.getRole());
            messageJson.put("content", msg.getContent());
            messageJson.put("content_type", msg.getContentType());
            additionalMessages.add(messageJson);
        }
        json.set("additional_messages", additionalMessages);

        post.setEntity(new StringEntity(json.toString(), StandardCharsets.UTF_8));
        //log.info("sendpost:{}", json);

        return httpClient.execute(post);
    }

    @Override
    public List<ChatRecodeVo> fetchRecord(String userId) {
        // 获取聊天记录
        List<PdChat> chatList = this.lambdaQuery().eq(PdChat::getUserId, userId).orderByAsc(PdChat::getSendTime).list();
        if (chatList == null || chatList.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取用户信息
        PdGuestUsers guestUsers = pdGuestUsersService.getById(userId);
        String userName = guestUsers != null ? guestUsers.getName() : "未知用户";

        List<ChatRecodeVo> result = new ArrayList<>();
        Date lastTimestamp = null;

        for (PdChat chat : chatList) {
            // 插入时间消息
            Date sendTime = chat.getSendTime();
            if (lastTimestamp == null || isTimeDiffExceeds(lastTimestamp, sendTime, 5)) {
                ChatRecodeVo timeMessage = new ChatRecodeVo();
                timeMessage.setType("time");
                timeMessage.setValue(formatDate(sendTime));
                result.add(timeMessage);
                lastTimestamp = sendTime;
            }

            // 插入聊天记录
            ChatRecodeVo message = new ChatRecodeVo();
            if (chat.getSendType() == 0) { // 用户消息
                message.setType("right");
                message.setUserName(userName);
            } else if (chat.getSendType() == 1) { // 客服消息
                message.setType("left");
                message.setUserName("客服");
            }
            message.setValue(chat.getMessage());
            result.add(message);
        }

        return result;
    }

    @Override
    public void addChatConfig(PdChatConfig entity) {
        PdChatConfig chatConfig = pdChatConfigService.lambdaQuery().eq(PdChatConfig::getSettingType, entity.getSettingType()).one();
        if (Objects.isNull(chatConfig)) {
            pdChatConfigService.save(entity);
            return;
        }
        pdChatConfigService.removeById(chatConfig.getId());
        pdChatConfigService.save(entity);
    }

    @Override
    public PdChatConfig getSetting(String type) {
        PdChatConfig chatConfig = pdChatConfigService.lambdaQuery().eq(PdChatConfig::getSettingType, type).one();
        if (Objects.isNull(chatConfig)) {
            throw new RuntimeException("未找到配置");
        }
        return chatConfig;
    }

    @Override
    public IPage<PdChat> pageList(Page<PdChat> page, Wrapper<PdChat> queryWrapper) {
        return this.baseMapper.pageList(page, queryWrapper);
    }

    @Override
    public IPage<PdChat> queryPageList(Page<PdChat> page, String dateRange, String tenantId) {
        QueryWrapper<PdChat> queryWrapper = new QueryWrapper<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户没有权限查看所有租户数据，则按照原来的逻辑限制租户ID
        if (!hasAllDataPermission) {
            queryWrapper.in("c.tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
        }
        // 如果用户有权限查看所有租户数据，则不添加租户ID过滤条件，但如果传入了特定的租户ID，则只查询该租户的数据

        if (StringUtils.isNotBlank(tenantId)) {
            queryWrapper.eq("c.tenant_id", tenantId);
        }

        String startDateStr = null;
        String endDateStr = null;

        if (StringUtils.isNotBlank(dateRange)) {
            String[] dates = dateRange.split(",");
            if (dates.length == 2) {
                startDateStr = dates[0].trim();
                endDateStr = dates[1].trim();
            }
        }

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantUtils.getCurrentTenantId());

        // 处理时间范围，确保不超过当前时间
        startDateStr = timeRestrictionUtil.processStartDate(startDateStr, canViewFutureData);
        endDateStr = timeRestrictionUtil.processEndDate(endDateStr, canViewFutureData);

        if (StringUtils.isNotBlank(startDateStr)) {
            String startDateTime = startDateStr + " 00:00:00";
            queryWrapper.ge("c.send_time", startDateTime); // 起始时间
        }
        if (StringUtils.isNotBlank(endDateStr)) {
            String endDateTime = endDateStr + " 23:59:59";
            queryWrapper.le("c.send_time", endDateTime);   // 结束时间
        }

        // 使用XML联查方式获取游客名称
        return this.baseMapper.pageList(page, queryWrapper);
    }

    @Override
    public IPage<PdChat> exportPageList(Page<PdChat> page, org.jeecg.modules.wechat.dto.PdChatExportRequest exportRequest) {
        if (ObjectUtil.isEmpty(exportRequest.getTenantId())) {
            // 获取当前请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                // 从请求头获取租户 ID
                String tenantId = request.getHeader(CommonConstant.TENANT_ID);
                exportRequest.setTenantId(tenantId);
            }
        }
        return this.baseMapper.exportPageList(page, exportRequest);
    }

    @Override
    public List<PdChatVO> userChat() {
        return userChat(5); // 默认返回5条记录
    }

    @Override
    public List<PdChatVO> userChat(Integer limit) {
        int randomNum = new Random().nextInt(2);
        if (randomNum == 0) {return new ArrayList<>();}
        // 获取最新的指定条数聊天记录（按发送时间倒序）
        List<PdChat> chatList = this.lambdaQuery()
                .select(PdChat::getSendTime, PdChat::getMessage)
                .orderByDesc(PdChat::getSendTime)
                .last("LIMIT " + limit)
                .list();

        // 如果查询结果为空，直接返回空列表，避免 NullPointerException
        if (CollUtil.isEmpty(chatList)) {
            return Collections.emptyList();
        }

        // 转换为 PdChatVO，并格式化时间为 HH:mm:ss
        return chatList.stream().map(chat -> {
            PdChatVO chatVO = new PdChatVO();
            chatVO.setSendTime(DateUtils.formatDate(chat.getSendTime(), "HH:mm:ss"));
            chatVO.setMessage(chat.getMessage());
            return chatVO;
        }).collect(Collectors.toList());
    }

    /**
     * 判断两个时间是否超过指定分钟的差距
     */
    private boolean isTimeDiffExceeds(Date previous, Date current, int  minutes) {
        long diffMillis = current.getTime() - previous.getTime();
        return diffMillis >= minutes * 60 * 1000;
    }

    /**
     * 格式化日期为 "yyyy-MM-dd HH:mm:ss"
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    public ReplyVO pollReplyVO(ChatIdVO chatIdVO) {
        ReplyVO replyVO = null;

        for (int i = 0; i < 5; i++) {
            // 调用获取 ReplyVO 的方法
            replyVO = getReplyVO(chatIdVO);

            // 检查内容是否为空或空白
            if (StrUtil.isNotBlank(replyVO.getContent())) {
                return replyVO; // 返回结果
            }

            // 如果未获取到内容，则睡眠 1 秒后继续轮询
            try {
                log.info("第 " + (i + 1) + " 次轮询，未获取到内容，正在等待...");
                Thread.sleep(8000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复线程中断状态
                throw new RuntimeException("轮询被中断", e);
            }
        }

        // 如果 5 次轮询仍未返回内容，抛出异常
        throw new RuntimeException("轮询超时，消息还未返回");
    }

    private  ReplyVO getReplyVO(ChatIdVO chatIdVO) {
        ChatResponseVo messageList = getMessageList(chatIdVO.getId(), chatIdVO.getConversationId());
        ReplyVO replyVO = new ReplyVO();
        messageList.getData().forEach(item->{
            if ("answer".equals(item.getType()) && !"{".equals(item.getContent())){
                //log.info("返回的消息内容：" + item.getContent());
                replyVO.setContent(item.getContent());

            }
        });
        return replyVO;
    }

    private void checkChatStatus(ChatIdVO chatIdVO, String dto) throws IOException, InterruptedException {
        // 检查 dto 中是否包含“人工”
//        if (dto.contains("人工")) {
//            executeWorkflow(dto);
//            return;  // 不再继续执行
//        }

        String checkUrl = "https://api.coze.cn/v3/chat/retrieve?chat_id=" + chatIdVO.getId() + "&conversation_id=" + chatIdVO.getConversationId();
        int retryCount = 0;

        String accessToken = null;
        try {
            accessToken = cozeAuthService.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        while (retryCount < MAX_RETRY_COUNT) {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet get = new HttpGet(checkUrl);
                get.setHeader("Authorization", "Bearer " + accessToken);
                get.setHeader("Content-Type", "application/json");

                try (CloseableHttpResponse response = httpClient.execute(get)) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject res = JSONObject.parseObject(responseBody);
                    JSONObject data = res.getJSONObject("data");

                    if (res.getIntValue("code") == 0 && ObjectUtil.isNotEmpty(data)) {
                        String status = data.getString("status");
                        if ("completed".equals(status)) {
                            System.out.println("对话已完成");
                            break;
                        } else {
                            System.out.println("对话未完成，当前状态为 " + status + "，等待 1.5 秒后重试...");
                            Thread.sleep(1500);
                            retryCount++;
                        }
                    }
                }
            }
        }
    }

    // 执行工作流方法
    private void executeWorkflow(String dto) {
        String url = "https://api.coze.cn/v1/workflow/run";
        HttpPost post = new HttpPost(url);
        post.setHeader("Authorization", author);
        post.setHeader("Content-Type", "application/json");

        JSONObject parameters = new JSONObject();
        parameters.put("BOT_USER_INPUT", dto);

        JSONObject requestBody = new JSONObject();
        requestBody.put("workflow_id", workflowId);
        requestBody.put("parameters", parameters);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            post.setEntity(new StringEntity(requestBody.toString(), "UTF-8"));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JSONObject res = JSONObject.parseObject(responseBody);

                // 打印返回的 data 字段
                if (res.getIntValue("code") == 0) {
                    System.out.println("工作流执行结果: " + res.getString("data"));
                } else {
                    System.out.println("工作流执行失败: " + res.getString("msg"));
                }
            }
        } catch (IOException e) {
            System.out.println("调用工作流接口时发生错误：" + e.getMessage());
        }
    }

    private CloseableHttpResponse sendChatMessage(SendChatDto chatDto) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost post = new HttpPost(sendUrl);
        String accessToken = null;
        try {
            accessToken = cozeAuthService.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 从 Redis 获取 chat_id
        Object chatId = redisTemplate.opsForValue().get("chat_id_" + chatDto.getUserId());
        if (ObjectUtil.isNotEmpty(chatId)) {
            String conversationId = chatId.toString();
            post.setURI(URI.create(sendUrl + "?conversation_id=" + conversationId));
        }

        //post.setHeader("Authorization", author); // 确保使用了正确的令牌
        post.setHeader("Authorization", "Bearer " + accessToken);
        post.setHeader("Content-Type", "application/json");

        // 创建符合目标格式的 JSON 对象
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode json = objectMapper.createObjectNode();
        json.put("bot_id", chatDto.getBotId());
        json.put("user_id", chatDto.getUserId());
        json.put("auto_save_history", chatDto.isAutoSaveHistory());
        json.put("stream", chatDto.isStream());

        ArrayNode additionalMessages = objectMapper.createArrayNode();
        for (AdditionalMessageDto msg : chatDto.getAdditionalMessages()) {
            ObjectNode messageJson = objectMapper.createObjectNode();
            messageJson.put("role", msg.getRole());
            messageJson.put("content", msg.getContent());
            messageJson.put("content_type", msg.getContentType());
            additionalMessages.add(messageJson);
        }
        json.set("additional_messages", additionalMessages);

        post.setEntity(new StringEntity(json.toString(), StandardCharsets.UTF_8));
        //log.info("sendpost:{}", json);

        return httpClient.execute(post);
    }

    private ChatIdVO handleChatResponse(SendChatDto chatDto, CloseableHttpResponse response) throws IOException, InterruptedException {
        ChatIdVO chatIdVO = new ChatIdVO();
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject res = JSONObject.parseObject(responseBody);
        JSONObject data = res.getJSONObject("data");

        if (ObjectUtil.isNotEmpty(data) && ObjectUtil.isNotEmpty(data.getString("id"))) {
            String chatId = data.getString("id");
            String conversationId = data.getString("conversation_id");
            chatIdVO.setConversationId(conversationId).setId(chatId);

            if (ObjectUtil.isEmpty(redisTemplate.opsForValue().get("chat_id_" + chatDto.getUserId()))) {
                redisTemplate.opsForValue().set("chat_id_" + chatDto.getUserId(), conversationId, 10, TimeUnit.MINUTES);
            }

            String status = data.getString("status");
            if ("in_progress".equals(status)) {
                //log.info("对话正在处理中，开始轮询状态...");
                String content = chatDto.getAdditionalMessages().get(0).getContent();
                checkChatStatus(chatIdVO,content);
            } else if ("completed".equals(status)) {
                chatIdVO.setId(chatId).setConversationId(conversationId);
            }

            return chatIdVO;
        }
        return null; // 响应不成功
    }

    private ChatResponseVo getMessageList(String id, String conversationId) {
        ChatResponseVo chatResponseVo = null;
        String accessToken = null;
        try {
            accessToken = cozeAuthService.getAccessToken();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<ChatDataVo> chatDataVoList = new ArrayList<>();
        String url = String.format("%s?chat_id=%s&conversation_id=%s", listUrl, id, conversationId);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet get = new HttpGet(url);
            get.setHeader("Authorization", "Bearer " + accessToken);
            //get.setHeader("Authorization", author); // 添加授权头

            try (CloseableHttpResponse response = httpClient.execute(get)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (ObjectUtil.isEmpty(jsonObject.getJSONArray("data"))) {
                    //log.info("调用返回参数为空");
                    throw new RuntimeException("返回参数为空");
                }
                JSONArray data = jsonObject.getJSONArray("data");
                for (int i = 0; i < data.size(); i++) {
                    JSONObject json = data.getJSONObject(i);
                    if (!"answer".equals(json.getString("type")) || "你".equals(json.getString("content"))){
                        continue;
                    }
                    ChatDataVo chatDataVo = new ChatDataVo();
                    chatDataVo.setBotId(json.getString("bot_id"));
                    chatDataVo.setChatId(json.getString("chat_id"));
                    chatDataVo.setContent(json.getString("content"));
                    chatDataVo.setContentType(json.getString("content_type"));
                    chatDataVo.setConversationId(json.getString("conversation_id"));
                    chatDataVo.setId(json.getString("id"));
                    chatDataVo.setRole(json.getString("role"));
                    chatDataVo.setType(json.getString("type"));
                    chatDataVoList.add(chatDataVo);
                }

                chatResponseVo = new ChatResponseVo();
                chatResponseVo.setCode(jsonObject.getInteger("code"));
                chatResponseVo.setData(chatDataVoList);
                chatResponseVo.setMsg(jsonObject.getString("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return chatResponseVo;
    }

}
