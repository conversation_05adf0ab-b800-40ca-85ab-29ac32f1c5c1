package org.jeecg.modules.wechat.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.wechat.config.PdChatConfig;
import org.jeecg.modules.wechat.dto.SendMessageDto;
import org.jeecg.modules.wechat.entity.PdChat;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wechat.vo.PdChatVO;
import org.jeecg.modules.wechat.vo.chat.*;

import java.util.List;

/**
 * @Description: 聊天记录
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
public interface IPdChatService extends IService<PdChat> {

    ChatIdVO sendMessage(SendMessageDto dto);

    ReplyVO getTheMessage(ChatIdVO dto);

    String getAiMessage(String content, String botId);

    String buttonRecord(String content, String botId);

    List<ChatRecodeVo> fetchRecord(String userId);

    void addChatConfig(PdChatConfig entity);

    PdChatConfig getSetting(String type);

    /**
     * 分页查询聊天记录，并联查游客名称
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<PdChat> pageList(Page<PdChat> page, Wrapper<PdChat> queryWrapper);

    /**
     * 分页查询聊天记录（包含时间范围判断）
     *
     * @param page 分页参数
     * @param dateRange 日期范围
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<PdChat> queryPageList(Page<PdChat> page, String dateRange, String tenantId);

    /**
     * 导出查询聊天记录，使用DTO参数
     *
     * @param page 分页参数
     * @param exportRequest 导出请求参数
     * @return 分页结果
     */
    IPage<PdChat> exportPageList(Page<PdChat> page, org.jeecg.modules.wechat.dto.PdChatExportRequest exportRequest);

    List<PdChatVO> userChat();

    /**
     * 获取指定条数的用户聊天记录
     *
     * @param limit 限制返回条数
     * @return 聊天记录列表
     */
    List<PdChatVO> userChat(Integer limit);
}
