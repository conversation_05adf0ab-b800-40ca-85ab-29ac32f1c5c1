package org.jeecg.modules.corp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 城市统计查询DTO
 * 用于根据省份code查询其下属城市的预约统计数据
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "城市统计查询DTO", description = "城市统计查询参数")
public class CityStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 省份代码（必填）
     */
    @ApiModelProperty(value = "省份代码", example = "130000", required = true)
    private String provinceCode;

    /**
     * 查询日期
     */
    @ApiModelProperty(value = "查询日期", example = "2025-06-15")
    private String queryDate;

    /**
     * 租户ID（可选，为空则查询所有租户）
     */
    @ApiModelProperty(value = "租户ID", example = "1")
    private Integer tenantId;

    public CityStatisticsDto() {
    }

    public CityStatisticsDto(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public CityStatisticsDto(String provinceCode, String queryDate) {
        this.provinceCode = provinceCode;
        this.queryDate = queryDate;
    }

    public CityStatisticsDto(String provinceCode, String queryDate, Integer tenantId) {
        this.provinceCode = provinceCode;
        this.queryDate = queryDate;
        this.tenantId = tenantId;
    }
}
