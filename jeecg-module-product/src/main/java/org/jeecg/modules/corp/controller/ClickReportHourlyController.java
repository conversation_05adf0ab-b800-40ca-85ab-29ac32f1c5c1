package org.jeecg.modules.corp.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.ClickDto;
import org.jeecg.modules.corp.dto.HourlyDto;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.jeecg.modules.corp.vo.TodayClickCountVo;
import org.jeecg.modules.wechat.dto.config.RegionConfigDTO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.jeecg.modules.corp.vo.ProvinceStatisticsVO;
import org.jeecg.modules.corp.dto.ProvinceStatisticsDto;
import org.jeecg.modules.corp.vo.CityStatisticsVO;
import org.jeecg.modules.corp.dto.CityStatisticsDto;
import org.jeecg.modules.corp.vo.hour.ReportTotalVO;
import org.jeecg.modules.corp.vo.report.ClickReportHourlyVO;
import org.jeecg.modules.corp.vo.report.ReportColumnarVO;
import org.jeecg.modules.corp.vo.report.ReportCoordinateVO;
import org.jeecg.modules.corp.vo.report.RegionCoordinateVO;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* <p>
 * 按小时统计点击报表前端控制器
 * </p>
*
* <AUTHOR>
* @since 2025-05-12
*/
@Api(tags = "按小时统计点击报表")
@RestController
@Slf4j
@RequestMapping("click-report-hourly")
public class ClickReportHourlyController {
    @Autowired
    private IClickReportHourlyService iClickReportHourlyService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysDeployConfigService sysDeployConfigService;

    @ApiOperation("获取大屏统计数据")
    @PostMapping("/getScreenDate")
    public Result<ClickReportHourlyVO> getScreenDate( @RequestBody ClickDto dto) {
        ClickReportHourlyVO clickReportHourlyVO =iClickReportHourlyService.getScreenDate(dto);
        return Result.ok(clickReportHourlyVO);
    }

    @ApiOperation("获取大屏城市柱状图")
    @PostMapping("/getColumnarDate")
    public Result<List<ReportColumnarVO>> getColumnarDate() {
        List<ReportColumnarVO> resList =iClickReportHourlyService.getColumnarDate();
        return Result.ok(resList);
    }

    @ApiOperation("获取大屏城市发散图")
    @PostMapping("/getCoordinate")
    public Result<RegionCoordinateVO> getCoordinate() {
        RegionCoordinateVO result = iClickReportHourlyService.getRegionCoordinate();
        return Result.ok(result);
    }

//    @ApiOperation("获取地区坐标信息（总部+子城市结构）")
//    @PostMapping("/getRegionCoordinate")
//    public Result<RegionCoordinateVO> getRegionCoordinate() {
//        RegionCoordinateVO result = iClickReportHourlyService.getRegionCoordinate();
//        return Result.ok(result);
//    }






    /**
     * 获取今日统计数据（点击数和预约总数）
     *
     * @param tenantId 租户ID（可选参数，如果不传则根据当前用户的bindTenant获取）
     * @return 今日统计数据结果
     */
    @ApiOperation("获取今日统计数据（点击数和预约总数）")
    @GetMapping("/getTodayClickCount")
    @ApiImplicitParam(name = "tenantId", value = "租户ID", dataType = "Integer", paramType = "query", required = false)
    public Result<TodayClickCountVo> getTodayClickCount(@RequestParam(value = "tenantId", required = false) Integer tenantId) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                LoginUser user = new LoginUser();
                user.setUsername("admin");
                loginUser = user;
            }

            // 获取地区配置
            RegionConfigDTO regionConfig = sysDeployConfigService.getDeployConfigByType(6, RegionConfigDTO.class);

            // 确定要查询的租户ID
            Integer queryTenantId = tenantId;

            // 如果没有传入租户ID，则根据当前用户的bindTenant获取
            if (queryTenantId == null) {
                // 通过用户名获取完整的用户信息（包含bindTenant）
                SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
                if (sysUser != null && oConvertUtils.isNotEmpty(sysUser.getBindTenant())) {
                    queryTenantId = Integer.valueOf(sysUser.getBindTenant());
                } else {
                    // 如果用户没有绑定租户，则设置为0（查询所有租户）
                    queryTenantId = 0;
                }
            }

            // 根据配置的点击变化逻辑处理租户条件
            if (regionConfig != null && regionConfig.getClickChangeLogic() != null) {
                if (regionConfig.getClickChangeLogic() == 0) {
                    // 按照全租户统计，将租户条件设置为null
                    queryTenantId = null;
                }
                // 如果是1，则保持原有的queryTenantId不变（按照右侧子公司点击变化）
            }

            // 调用Service获取今日点击数
            Integer todayClickCount = iClickReportHourlyService.getTodayClickCount(queryTenantId);

            // 调用Service获取今日预约总数
            Integer todayReservationCount = iClickReportHourlyService.getTodayReservationCount(queryTenantId);

            // 构造返回结果
            TodayClickCountVo result = new TodayClickCountVo(todayClickCount, todayReservationCount, queryTenantId);


            return Result.ok(result);

        } catch (Exception e) {
            TodayClickCountVo clickCountVo = new TodayClickCountVo();
            clickCountVo.setTenantId(0);
            clickCountVo.setTodayClickCount(0);
            clickCountVo.setTodayReservationCount(0);
            return Result.ok(clickCountVo);
        }
    }

    @ApiOperation("获取今日统计数据（点击数和预约总数）")
    @GetMapping("/getTodayClick")
    @ApiImplicitParam(name = "tenantId", value = "租户ID", dataType = "Integer", paramType = "query", required = false)
    public Result<TodayClickCountVo> getTodayClick(@RequestParam(value = "tenantId", required = false) Integer tenantId) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                LoginUser user = new LoginUser();
                user.setUsername("admin");
                loginUser = user;
            }

            // 确定要查询的租户ID
            Integer queryTenantId = tenantId;

            // 如果没有传入租户ID，则根据当前用户的bindTenant获取
            if (queryTenantId == null) {
                // 通过用户名获取完整的用户信息（包含bindTenant）
                SysUser sysUser = sysUserService.getUserByName(loginUser.getUsername());
                if (sysUser != null && oConvertUtils.isNotEmpty(sysUser.getBindTenant())) {
                    queryTenantId = Integer.valueOf(sysUser.getBindTenant());
                } else {
                    // 如果用户没有绑定租户，则设置为0（查询所有租户）
                    queryTenantId = 0;
                }
            }

            // 调用Service获取今日点击数
            Integer todayClickCount = iClickReportHourlyService.getTodayClickCount(queryTenantId);

            // 调用Service获取今日预约总数
            Integer todayReservationCount = iClickReportHourlyService.getTodayReservationCount(queryTenantId);

            // 构造返回结果
            TodayClickCountVo result = new TodayClickCountVo(todayClickCount, todayReservationCount, queryTenantId);


            return Result.ok(result);

        } catch (Exception e) {
            TodayClickCountVo clickCountVo = new TodayClickCountVo();
            clickCountVo.setTenantId(0);
            clickCountVo.setTodayClickCount(0);
            clickCountVo.setTodayReservationCount(0);
            return Result.ok(clickCountVo);
        }
    }

    /**
     * 根据租户ID添加点击数
     *
     * @param tenantId 租户ID
     * @return 操作结果
     */
    @ApiOperation("根据租户ID添加点击数")
    @PostMapping("/addClickByTenantId")
    @ApiImplicitParam(value = "租户ID", name = "tenantId", dataTypeClass = Integer.class, required = true)
    public Result<String> addClickByTenantId(@RequestParam Integer tenantId) {
        try {
            if (tenantId == null) {
                return Result.error("租户ID不能为空");
            }

            // 调用Service添加点击数
            iClickReportHourlyService.addClickByTenantId(tenantId);

            return Result.ok("点击数添加成功");

        } catch (Exception e) {
            log.error("添加点击数失败，租户ID: " + tenantId, e);
            return Result.error("添加点击数失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有一级省份的统计数据（预约总数、城市code、经纬度）
     * 使用多线程分别查询车险、财险、增值服务三种类型的数据
     * 查询参数：日期为今日
     *
     * @return 一级省份统计数据列表（包含城市code、经纬度、预约总数）
     */
    @ApiOperation("获取所有一级省份的统计数据（多线程查询）")
    @GetMapping("/getProvinceStatistics")
    public Result<List<ProvinceStatisticsVO>> getProvinceStatistics() {
        try {
            log.info("开始获取一级省份统计数据，使用多线程分别查询车险、财险、增值服务");

            // 获取今日日期
            String today = java.time.LocalDate.now().toString();

            // 创建查询参数DTO
            ProvinceStatisticsDto queryDto = new ProvinceStatisticsDto(today);

            // 调用Service层的多线程查询方法
            List<ProvinceStatisticsVO> resultList = iClickReportHourlyService.getProvinceStatisticsWithMultiThread(queryDto);

            log.info("一级省份统计数据查询完成，共{}条记录", resultList.size());
            return Result.ok(resultList);

        } catch (Exception e) {
            log.error("获取一级省份统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据省份code查询其所有子城市的预约总数
     * 使用多线程分别查询车险、财险、增值服务三种类型的数据
     *
     * @param dto 查询参数（包含省份code、查询日期、租户ID）
     * @return 子城市预约统计数据列表
     */
    @ApiOperation("根据省份code查询子城市预约统计数据（多线程查询）")
    @PostMapping("/getCityByProvinceCode")
    public Result<List<CityStatisticsVO>> getCityStatisticsByProvinceCode(@RequestBody CityStatisticsDto dto) {
        try {
            // 如果没有传入查询日期，使用今日
            if (dto.getQueryDate() == null || dto.getQueryDate().trim().isEmpty()) {
                dto.setQueryDate(java.time.LocalDate.now().toString());
            }

            // 调用Service层的多线程查询方法
            List<CityStatisticsVO> resultList = iClickReportHourlyService.getCityStatisticsByProvinceCode(dto);

            return Result.ok(resultList);

        } catch (Exception e) {
            log.error("查询省份{}下子城市预约统计数据失败", dto.getProvinceCode(), e);
            return Result.error("查询子城市统计数据失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取租户时段点击数据")
    @PostMapping("/getHourDate")
    public Result<ReportTotalVO> getHourDate( @RequestBody HourlyDto dto) {
        ReportTotalVO clickReportHourlyVO =iClickReportHourlyService.getHourDate(dto);
        return Result.ok(clickReportHourlyVO);
    }


}