package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.corp.entity.PdChatUser;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdGuestUsers;

import java.util.List;

/**
 * @Description: 聊天用户
 * @Author: jeecg-boot
 * @Date:   2024-11-09
 * @Version: V1.0
 */
public interface IPdChatUserService extends IService<PdChatUser> {

    /**
     * 分页查询聊天用户
     *
     * @param page 分页参数
     * @param pdChatUser 查询条件
     * @param dateRange 日期范围
     * @return 分页结果
     */
    IPage<PdChatUser> queryPageList(Page<PdChatUser> page, PdChatUser pdChatUser, String dateRange);

}
