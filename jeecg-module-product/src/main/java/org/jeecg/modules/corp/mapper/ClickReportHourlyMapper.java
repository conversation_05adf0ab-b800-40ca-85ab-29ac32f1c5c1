package org.jeecg.modules.corp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.vo.report.ReportColumnarVO;
import org.jeecg.modules.corp.vo.ProvinceStatisticsVO;
import org.jeecg.modules.corp.dto.ProvinceStatisticsDto;
import org.jeecg.modules.corp.dto.CityStatisticsDto;
import org.jeecg.modules.corp.vo.ProvinceStatisticsVO;
import org.jeecg.modules.corp.dto.ProvinceStatisticsDto;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Mapper
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Mapper
public interface ClickReportHourlyMapper extends BaseMapper<ClickReportHourly> {

    /**
     * 根据日期范围获取点击统计数据
     *
     * @param params 参数Map，包含startDate, endDate, configType, tenantId等参数
     * @return 统计数据列表
     */
    List<Map<String, Object>> getClickStatsByDateRange(@Param("params") Map<String, Object> params);

    /**
     * 获取今日点击数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日点击数总和
     */
    Integer getTodayClickCount(@Param("tenantId") Integer tenantId);

    /**
     * 根据租户ID添加点击数
     *
     * @param tenantId 租户ID
     */
    void addClickByTenantId(@Param("tenantId") Integer tenantId);

    /**
     * 获取今日预约总数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日预约总数
     */
    Integer getTodayReservationCount(@Param("tenantId") Integer tenantId);

    /**
     * 获取时段曲线图数据
     *
     * @param tenantId 租户ID
     * @param orderDate 查询日期
     * @return 时段曲线图数据列表
     */
    List<Map<String, Object>> getCurveData(@Param("tenantId") String tenantId, @Param("orderDate") String orderDate);

    /**
     * 获取城市汇总数据
     *
     * @param tenantId 租户ID
     * @param orderDate 查询日期
     * @return 城市汇总数据列表
     */
    List<Map<String, Object>> getCityData(@Param("tenantId") String tenantId, @Param("orderDate") String orderDate);

    /**
     * 获取指标类数据
     *
     * @param tenantId 租户ID
     * @param orderDate 查询日期
     * @return 指标数据
     */
    Map<String, Object> getRecodeData(@Param("tenantId") String tenantId, @Param("orderDate") String orderDate);

    List<ReportColumnarVO> getColumnarDate();

    /**
     * 获取小时维度的点击数据（查询日期和对比日期）
     *
     * @param dto 查询参数
     * @return 小时点击数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyClickVO> getHourlyClickData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取小时维度的预约数据（查询日期和对比日期）
     *
     * @param dto 查询参数
     * @return 小时预约数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyReservationVO> getHourlyReservationData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取周期维度的点击数据（天/周/月汇总）
     *
     * @param dto 查询参数
     * @return 周期点击数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyClickVO> getPeriodClickData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取周期维度的预约数据（天/周/月汇总）
     *
     * @param dto 查询参数
     * @return 周期预约数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyReservationVO> getPeriodReservationData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取按天汇总的点击数据
     *
     * @param dto 查询参数
     * @return 按天汇总的点击数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyClickVO> getDailyClickData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取按天汇总的预约数据
     *
     * @param dto 查询参数
     * @return 按天汇总的预约数据列表
     */
    List<org.jeecg.modules.corp.vo.hour.HourlyReservationVO> getDailyReservationData(@Param("dto") org.jeecg.modules.corp.dto.HourlyDto dto);

    /**
     * 获取省份预约统计数据（包含城市code和经纬度）
     * 统计三个表的预约数据：pd_added, pd_car_info, pd_casualty_info
     *
     * @param dto 查询参数
     * @return 省份统计数据列表
     */
    List<Map<String, Object>> getProvinceReservationStatistics(@Param("dto") ProvinceStatisticsDto dto);

    /**
     * 获取车险预约统计数据
     * @param dto 查询参数
     * @return 车险统计数据列表
     */
    List<Map<String, Object>> getCarInsuranceStatistics(@Param("dto") ProvinceStatisticsDto dto);

    /**
     * 获取财险预约统计数据
     * @param dto 查询参数
     * @return 财险统计数据列表
     */
    List<Map<String, Object>> getPropertyInsuranceStatistics(@Param("dto") ProvinceStatisticsDto dto);

    /**
     * 获取增值服务预约统计数据
     * @param dto 查询参数
     * @return 增值服务统计数据列表
     */
    List<Map<String, Object>> getValueAddedServiceStatistics(@Param("dto") ProvinceStatisticsDto dto);

    /**
     * 根据省份code查询车险子城市预约统计数据
     * @param dto 查询参数（包含省份code）
     * @return 车险子城市统计数据列表
     */
    List<Map<String, Object>> getCityCarInsuranceStatistics(@Param("dto") CityStatisticsDto dto);

    /**
     * 根据省份code查询财险子城市预约统计数据
     * @param dto 查询参数（包含省份code）
     * @return 财险子城市统计数据列表
     */
    List<Map<String, Object>> getCityPropertyInsuranceStatistics(@Param("dto") CityStatisticsDto dto);

    /**
     * 根据省份code查询增值服务子城市预约统计数据
     * @param dto 查询参数（包含省份code）
     * @return 增值服务子城市统计数据列表
     */
    List<Map<String, Object>> getCityValueAddedServiceStatistics(@Param("dto") CityStatisticsDto dto);

}