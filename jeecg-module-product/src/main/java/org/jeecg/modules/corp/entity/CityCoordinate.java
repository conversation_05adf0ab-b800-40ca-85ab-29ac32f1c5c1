package org.jeecg.modules.corp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 一级城市坐标信息实体类
 * @Author: jeecg-boot
 * @Date: 2025-01-XX
 */
@Data
@TableName("city_coordinate")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CityCoordinate对象", description="一级城市坐标信息")
public class CityCoordinate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", example = "北京市")
    private String cityName;

    /**
     * 行政区划代码
     */
    @ApiModelProperty(value = "行政区划代码", example = "110000")
    private String adcode;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度", example = "116.407526")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度", example = "39.904030")
    private BigDecimal latitude;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 是否启用 (0-禁用 1-启用)
     */
    @ApiModelProperty(value = "是否启用", example = "1")
    private Integer status;
}
