package org.jeecg.modules.corp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.corp.dto.ClickDto;
import org.jeecg.modules.corp.dto.HourlyDto;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.mapper.ClickReportHourlyMapper;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.corp.vo.hour.HourlyClickDataVO;
import org.jeecg.modules.corp.vo.hour.HourlyReservationDataVO;
import org.jeecg.modules.corp.vo.hour.ReportStructureVO;
import org.jeecg.modules.corp.vo.hour.ReportHourlyVO;
import org.jeecg.modules.corp.vo.hour.ReportTotalVO;
import org.jeecg.modules.corp.vo.hour.HourlyClickVO;
import org.jeecg.modules.corp.vo.hour.HourlyReservationVO;
import org.jeecg.modules.corp.vo.hour.ReportHourlyListVO;
import org.jeecg.modules.corp.vo.hour.ReportSummaryVO;
import org.jeecg.modules.corp.vo.report.*;
import org.jeecg.modules.corp.vo.ProvinceStatisticsVO;
import org.jeecg.modules.corp.dto.ProvinceStatisticsDto;
import org.jeecg.modules.corp.vo.CityStatisticsVO;
import org.jeecg.modules.corp.dto.CityStatisticsDto;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.jeecg.modules.corp.enums.ProvinceCoordinateEnum;
import org.jeecg.modules.wechat.dto.config.RegionConfigDTO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.DayOfWeek;

/**
 * @Description: 按小时统计点击报表Service实现
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Service
public class ClickReportHourlyServiceImpl extends ServiceImpl<ClickReportHourlyMapper, ClickReportHourly> implements IClickReportHourlyService {
    @Autowired
    private ISysDeployConfigService sysDeployConfigService;

    /**
     * 线程池用于多线程查询
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(3);


    @Override
    public List<Map<String, Object>> getClickStatsByDateRange(Map<String, Object> params) {
        return baseMapper.getClickStatsByDateRange(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly add(ClickReportHourly dto) {
        save(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly edit(ClickReportHourly dto) {
        updateById(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public IPage<ClickReportHourly> findPage(IPage<ClickReportHourly> page, ClickReportHourly dto) {
        return baseMapper.selectPage(page, null);
    }

    @Override
    public Integer getTodayClickCount(Integer tenantId) {
        return baseMapper.getTodayClickCount(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addClickByTenantId(Integer tenantId) {
        baseMapper.addClickByTenantId(tenantId);
    }

    @Override
    public Integer getTodayReservationCount(Integer tenantId) {
        return baseMapper.getTodayReservationCount(tenantId);
    }

    @Override
    public ClickReportHourlyVO getScreenDate(ClickDto dto) {
        if (dto.getTenantId().equals("0")) {
            dto.setTenantId(null);
        }
        ClickReportHourlyVO result = new ClickReportHourlyVO();
        //1. 获取时段曲线图,根据 dto 中的日期与租户 id 参数,查询基础数据click_report_hourly与预约表们按照创建时间与 dto 中的日期相等,需要将去除创建时间的时分秒,总数即可:pd_added,pd_car_info,pd_casualty_info,完成 xml 语句编写,查询出List<ReportCurveVO> curveList
        List<ReportCurveVO> curveList = getCurveList(dto);

        //2. 获取指标类数据,根据 dto 中的日期与租户 id 参数,查询PdRecodeVO基础数据pd_link_recode,pv和 uv 的汇总总数取click_report_hourly
        PdRecodeVO recodeVo = getRecodeVo(dto);

        //3.  获取城市数据,根据 dto 中的日期与租户 id 参数,查询基础数据预约表:pd_added,pd_car_info,pd_casualty_info中的城市汇总数据,完成 xml 语句编写
        List<ReportCityVO> cityRateList = getCityRateList(dto);

        result.setCurveList(curveList);
        result.setRecodeVo(recodeVo);
        result.setCityRateList(cityRateList);
        return result;
    }

    @Override
    public List<ReportColumnarVO> getColumnarDate() {
        List<ReportColumnarVO>  result = this.baseMapper.getColumnarDate();
        return result;
    }

    @Override
    public ReportTotalVO getHourDate(HourlyDto dto) {
        // 根据比较模式分别处理
        if ("0".equals(dto.getCompareMode())) {
            // 同比模式：根据维度类型使用不同的查询方式
            if ("0".equals(dto.getCompareType())) {
                // 小时同比：使用两天数据对比（orderDate vs compareDate）
                if (dto.getOrderDate() == null || dto.getCompareDate() == null) {
                    throw new IllegalArgumentException("小时同比查询需要提供orderDate和compareDate参数");
                }
                return getHourlyYearOverYearData(dto);
            } else {
                // 天/周/月同比：使用时间范围数据对比（startDate~endDate vs compareStartDate~compareEndDate）
                if (dto.getStartDate() == null || dto.getEndDate() == null ||
                    dto.getCompareStartDate() == null || dto.getCompareEndDate() == null) {
                    throw new IllegalArgumentException("天/周/月同比查询需要提供startDate、endDate、compareStartDate、compareEndDate参数");
                }
                return getPeriodYearOverYearData(dto);
            }
        } else if ("1".equals(dto.getCompareMode())) {
            // 环比：使用现有的周期逻辑（天/周/月汇总）
            return getPeriodComparisonData(dto);
        } else {
            // 兼容旧版本：根据对比类型分别处理
            if ("0".equals(dto.getCompareType())) {
                // 小时对比处理
                return getHourlyComparisonData(dto);
            } else {
                // 天/周/月对比处理
                return getPeriodComparisonData(dto);
            }
        }
    }

    /**
     * 处理小时对比数据
     * @param dto 查询参数
     * @return 小时对比结果
     */
    private ReportTotalVO getHourlyComparisonData(HourlyDto dto) {
        // 验证小时查询参数
        if (dto.getOrderDate() == null || dto.getCompareDate() == null) {
            throw new IllegalArgumentException("小时查询需要提供orderDate和compareDate参数");
        }

        // 1. 查询点击数据
        List<HourlyClickVO> clickDataList = baseMapper.getHourlyClickData(dto);

        // 2. 查询预约数据
        List<HourlyReservationVO> reservationDataList = baseMapper.getHourlyReservationData(dto);

        // 3. 合并数据并封装成List<ReportStructureVO>
        List<ReportStructureVO> structureList = mergeHourlyData(clickDataList, reservationDataList);

        // 封装返回结果
        ReportTotalVO result = new ReportTotalVO();

        // 将ReportStructureVO转换为ReportHourlyVO用于periodData（只包含查询日期的数据）
        List<ReportHourlyVO> periodData = convertToReportHourlyVO(structureList, "0");
        result.setPeriodData(periodData);

        // 4. 计算环比数据
        List<ReportHourlyListVO> monthOnMonthData = calculateHourlyMonthOnMonthData(clickDataList, reservationDataList);
        result.setMonthOnMonthData(monthOnMonthData);

        // 5. 计算汇总数据
        ReportSummaryVO summaryData = calculateSummaryData(clickDataList, reservationDataList);
        result.setSummaryData(summaryData);

        return result;
    }

    /**
     * 获取小时同比数据（使用两天数据对比）
     * @param dto 查询参数
     * @return 小时同比结果
     */
    private ReportTotalVO getHourlyYearOverYearData(HourlyDto dto) {
        // 1. 查询两天的小时点击数据
        List<HourlyClickVO> clickDataList = baseMapper.getHourlyClickData(dto);

        // 2. 查询两天的小时预约数据
        List<HourlyReservationVO> reservationDataList = baseMapper.getHourlyReservationData(dto);

        // 3. 合并数据并封装成List<ReportStructureVO>
        List<ReportStructureVO> structureList = mergeHourlyData(clickDataList, reservationDataList);

        // 封装返回结果
        ReportTotalVO result = new ReportTotalVO();

        // 将ReportStructureVO转换为ReportHourlyVO用于periodData（只包含查询日期的数据）
        List<ReportHourlyVO> periodData = convertToReportHourlyVO(structureList, "0");
        result.setPeriodData(periodData);

        // 4. 计算同比数据
        List<ReportHourlyListVO> yearOverYearData = calculateHourlyYearOverYearData(clickDataList, reservationDataList);
        result.setMonthOnMonthData(yearOverYearData);

        // 5. 计算汇总数据
        ReportSummaryVO summaryData = calculateSummaryData(clickDataList, reservationDataList);
        result.setSummaryData(summaryData);

        return result;
    }

    /**
     * 获取周期同比数据（使用时间范围数据对比）
     * @param dto 查询参数
     * @return 周期同比结果
     */
    private ReportTotalVO getPeriodYearOverYearData(HourlyDto dto) {
        // 1. 先查询按天汇总的点击数据
        List<HourlyClickVO> dailyClickDataList = baseMapper.getDailyClickData(dto);

        // 2. 先查询按天汇总的预约数据
        List<HourlyReservationVO> dailyReservationDataList = baseMapper.getDailyReservationData(dto);

        // 3. 根据对比类型进行Java代码汇总
        List<HourlyClickVO> aggregatedClickData;
        List<HourlyReservationVO> aggregatedReservationData;

        if ("1".equals(dto.getCompareType())) {
            // 天同比，直接使用按天数据
            aggregatedClickData = dailyClickDataList;
            aggregatedReservationData = dailyReservationDataList;
        } else if ("2".equals(dto.getCompareType())) {
            // 周同比，按周汇总
            aggregatedClickData = aggregateClickDataByWeek(dailyClickDataList);
            aggregatedReservationData = aggregateReservationDataByWeek(dailyReservationDataList);
        } else if ("3".equals(dto.getCompareType())) {
            // 月同比，按月汇总
            aggregatedClickData = aggregateClickDataByMonth(dailyClickDataList);
            aggregatedReservationData = aggregateReservationDataByMonth(dailyReservationDataList);
        } else {
            // 默认按天处理
            aggregatedClickData = dailyClickDataList;
            aggregatedReservationData = dailyReservationDataList;
        }

        // 4. 合并数据并封装成List<ReportStructureVO>
        List<ReportStructureVO> structureList = mergePeriodData(aggregatedClickData, aggregatedReservationData, dto.getCompareType());

        // 封装返回结果
        ReportTotalVO result = new ReportTotalVO();

        // 将ReportStructureVO转换为ReportHourlyVO用于periodData（只包含查询日期的数据）
        List<ReportHourlyVO> periodData = convertToReportHourlyVO(structureList, "0");
        result.setPeriodData(periodData);

        // 5. 计算同比数据
        List<ReportHourlyListVO> yearOverYearData = calculatePeriodYearOverYearData(aggregatedClickData, aggregatedReservationData, dto.getCompareType());
        result.setMonthOnMonthData(yearOverYearData);

        // 6. 计算汇总数据
        ReportSummaryVO summaryData = calculateSummaryData(aggregatedClickData, aggregatedReservationData);
        result.setSummaryData(summaryData);

        return result;
    }


    /**
     * 处理周期对比数据（天/周/月）
     * @param dto 查询参数
     * @return 周期对比结果
     */
    private ReportTotalVO getPeriodComparisonData(HourlyDto dto) {
        // 验证周期查询参数
        if (dto.getStartDate() == null || dto.getEndDate() == null ||
            dto.getCompareStartDate() == null || dto.getCompareEndDate() == null) {
            throw new IllegalArgumentException("周期查询需要提供startDate、endDate、compareStartDate、compareEndDate参数");
        }

        // 1. 先查询按天汇总的点击数据
        List<HourlyClickVO> dailyClickDataList = baseMapper.getDailyClickData(dto);

        // 2. 先查询按天汇总的预约数据
        List<HourlyReservationVO> dailyReservationDataList = baseMapper.getDailyReservationData(dto);

        // 3. 根据对比类型进行Java代码汇总
        List<HourlyClickVO> aggregatedClickData;
        List<HourlyReservationVO> aggregatedReservationData;

        if ("1".equals(dto.getCompareType())) {
            // 天对比，直接使用按天数据
            aggregatedClickData = dailyClickDataList;
            aggregatedReservationData = dailyReservationDataList;
        } else if ("2".equals(dto.getCompareType())) {
            // 周对比，按周汇总
            aggregatedClickData = aggregateClickDataByWeek(dailyClickDataList);
            aggregatedReservationData = aggregateReservationDataByWeek(dailyReservationDataList);
        } else if ("3".equals(dto.getCompareType())) {
            // 月对比，按月汇总
            aggregatedClickData = aggregateClickDataByMonth(dailyClickDataList);
            aggregatedReservationData = aggregateReservationDataByMonth(dailyReservationDataList);
        } else {
            // 默认按天处理
            aggregatedClickData = dailyClickDataList;
            aggregatedReservationData = dailyReservationDataList;
        }

        // 4. 合并数据并封装成List<ReportStructureVO>
        List<ReportStructureVO> structureList = mergePeriodData(aggregatedClickData, aggregatedReservationData, dto.getCompareType());

        // 封装返回结果
        ReportTotalVO result = new ReportTotalVO();

        // 将ReportStructureVO转换为ReportHourlyVO用于periodData（只包含查询日期的数据）
        List<ReportHourlyVO> periodData = convertToReportHourlyVO(structureList, "0");
        result.setPeriodData(periodData);

        // 5. 计算环比数据
        List<ReportHourlyListVO> monthOnMonthData = calculatePeriodMonthOnMonthData(aggregatedClickData, aggregatedReservationData, dto.getCompareType());
        result.setMonthOnMonthData(monthOnMonthData);

        // 6. 计算汇总数据
        ReportSummaryVO summaryData = calculateSummaryData(aggregatedClickData, aggregatedReservationData);
        result.setSummaryData(summaryData);

        return result;
    }

    @Override
    public List<ReportCoordinateVO> reportCoordinateVO() {
        // 1.查询配置类
        RegionConfigDTO config = sysDeployConfigService.getDeployConfigByType(6, RegionConfigDTO.class);

        if (config == null) {
            return new ArrayList<>();
        }

        List<ReportCoordinateVO> result = new ArrayList<>();

        // 2.处理总部地区
        if (config.getHeadquarterName() != null) {
            ReportCoordinateVO headquarterVO = createCoordinateVO(config.getHeadquarterName());
            result.add(headquarterVO);
        }

        // 3.处理业务地区列表
        if (config.getBusinessRegions() != null && !config.getBusinessRegions().isEmpty()) {
            List<ReportCoordinateVO> businessVOs = config.getBusinessRegions().stream()
                .filter(region -> region.getRegionName() != null)
                .map(region -> createCoordinateVO(region.getRegionName()))
                .collect(Collectors.toList());
            result.addAll(businessVOs);
        }

        return result;
    }

    @Override
    public RegionCoordinateVO getRegionCoordinate() {
        // 1.查询配置类
        RegionConfigDTO config = sysDeployConfigService.getDeployConfigByType(6, RegionConfigDTO.class);

        RegionCoordinateVO result = new RegionCoordinateVO();

        if (config == null) {
            return result;
        }

        // 2.处理总部地区
        if (config.getHeadquarterName() != null) {
            RegionCoordinateVO.HeadquarterVO headquarterVO = createHeadquarterVO(config);
            result.setHeadquarter(headquarterVO);
        }

        // 3.处理业务地区列表
        List<ReportCoordinateVO> businessRegions = new ArrayList<>();
        if (config.getBusinessRegions() != null && !config.getBusinessRegions().isEmpty()) {
            businessRegions = config.getBusinessRegions().stream()
                .filter(region -> region.getRegionName() != null)
                .map(region -> createCoordinateVO(region.getRegionName()))
                .collect(Collectors.toList());
        }
        result.setBusinessRegions(businessRegions);

        return result;
    }

    /**
     * 获取时段曲线图数据
     * @param dto 查询参数
     * @return 时段曲线图数据列表
     */
    private List<ReportCurveVO> getCurveList(ClickDto dto) {
        List<Map<String, Object>> dataList = baseMapper.getCurveData(dto.getTenantId(), dto.getOrderDate());
        List<ReportCurveVO> curveList = new ArrayList<>();

        for (Map<String, Object> data : dataList) {
            ReportCurveVO curveVO = new ReportCurveVO();
            curveVO.setTimeFrame(String.valueOf(data.get("timeFrame")));
            curveVO.setClickPv(String.valueOf(data.get("clickPv")));
            curveVO.setClickNum(String.valueOf(data.get("clickNum")));
            curveVO.setCarInsuranceReservationNum(String.valueOf(data.get("carInsuranceReservationNum")));
            curveVO.setTravelInsuranceReservationNum(String.valueOf(data.get("travelInsuranceReservationNum")));
            curveVO.setAdditionalServiceReservationNum(String.valueOf(data.get("additionalServiceReservationNum")));
            curveVO.setReservationNum(String.valueOf(data.get("reservationNum")));

            // 计算转化率 = 预约总数/点击数
            Integer clickNum = Integer.valueOf(String.valueOf(data.get("clickNum")));
            Integer reservationNum = Integer.valueOf(String.valueOf(data.get("reservationNum")));
            if (clickNum > 0) {
                BigDecimal rate = new BigDecimal(reservationNum)
                    .divide(new BigDecimal(clickNum), 2, RoundingMode.HALF_UP);
                curveVO.setReservationRate(rate);
            } else {
                curveVO.setReservationRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }

            curveList.add(curveVO);
        }

        return curveList;
    }

    /**
     * 获取城市汇总数据
     * @param dto 查询参数
     * @return 城市汇总数据列表
     */
    private List<ReportCityVO> getCityRateList(ClickDto dto) {
        List<Map<String, Object>> dataList = baseMapper.getCityData(dto.getTenantId(), dto.getOrderDate());
        List<ReportCityVO> cityRateList = new ArrayList<>();

        // 计算总数用于计算占比
        int totalCount = 0;
        for (Map<String, Object> data : dataList) {
            Integer count = Integer.valueOf(String.valueOf(data.get("totalCount")));
            totalCount += count;
        }

        // 构建城市数据并计算占比
        for (Map<String, Object> data : dataList) {
            ReportCityVO cityVO = new ReportCityVO();
            cityVO.setCity(String.valueOf(data.get("city")));

            Integer count = Integer.valueOf(String.valueOf(data.get("totalCount")));
            if (totalCount > 0) {
                BigDecimal rate = new BigDecimal(count)
                    .divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP);
                cityVO.setCityRate(rate);
            } else {
                cityVO.setCityRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }

            cityRateList.add(cityVO);
        }

        return cityRateList;
    }

    /**
     * 获取指标类数据
     * @param dto 查询参数
     * @return 指标数据
     */
    private PdRecodeVO getRecodeVo(ClickDto dto) {
        Map<String, Object> data = baseMapper.getRecodeData(dto.getTenantId(), dto.getOrderDate());
        PdRecodeVO recodeVO = new PdRecodeVO();

        // 设置基础数据
        recodeVO.setClickPv(Integer.valueOf(String.valueOf(data.get("clickPv"))));
        recodeVO.setClickNum(Integer.valueOf(String.valueOf(data.get("clickNum"))));

        // 处理表单提交数：如果为0则随机生成3-12之间的数值
        Integer formSubmissions = Integer.valueOf(String.valueOf(data.get("formSubmissions")));
        if (formSubmissions == 0) {
            Random random = new Random();
            formSubmissions = random.nextInt(10) + 3; // 生成3-12之间的随机数
        }
        recodeVO.setFormSubmissions(formSubmissions);

        // 设置百分比数据，需要转换为BigDecimal并保留两位小数
        recodeVO.setBounceRate(formatBigDecimal(String.valueOf(data.get("bounceRate"))));
        recodeVO.setConversionRate(formatBigDecimal(String.valueOf(data.get("conversionRate"))));
        recodeVO.setCtr(formatBigDecimal(String.valueOf(data.get("ctr"))));
        recodeVO.setReturnRate(formatBigDecimal(String.valueOf(data.get("returnRate"))));
        recodeVO.setCompletionRate(formatBigDecimal(String.valueOf(data.get("completionRate"))));

        // 设置平均停留时长，保留两位小数
        recodeVO.setAvgStayTime(formatBigDecimal(String.valueOf(data.get("avgStayTime"))));

        recodeVO.setClickPvCompare(1).setClickNumCompare(1)
                .setBounceRateCompare(1).setConversionRateCompare(1)
                .setFormSubmissionsCompare(1).setCtrCompare(1)
                .setAvgStayTimeCompare(1).setReturnRateCompare(1)
                .setCompletionRateCompare(1);

        return recodeVO;
    }

    /**
     * 格式化BigDecimal，保留两位小数
     * @param value 原始值
     * @return 格式化后的BigDecimal
     */
    private BigDecimal formatBigDecimal(String value) {
        try {
            return new BigDecimal(value).setScale(2, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        }
    }



    /**
     * 创建坐标VO对象
     * @param cityName 城市名称
     * @return ReportCoordinateVO
     */
    private ReportCoordinateVO createCoordinateVO(String cityName) {
        ReportCoordinateVO coordinateVO = new ReportCoordinateVO();
        coordinateVO.setName(cityName);

        // 根据城市名称查找对应的省份坐标
        ProvinceCoordinateEnum province = ProvinceCoordinateEnum.fuzzyMatch(cityName);

        if (province != null) {
            // 使用真实的省份坐标和行政区划代码
            coordinateVO.setAdcode(province.getAdcode());
            List<Double> centerCoords = Arrays.asList(province.getLongitude(), province.getLatitude());
            coordinateVO.setCenter(centerCoords);
            coordinateVO.setCentroid(centerCoords);
        } else {
            // 如果未找到匹配的省份，生成随机坐标作为备用
            Random random = new Random(cityName.hashCode());
            String adcode = String.format("%06d", random.nextInt(999999));
            coordinateVO.setAdcode(adcode);

            // 生成中国范围内的随机坐标
            double longitude = 73.55 + (135.08 - 73.55) * random.nextDouble();
            double latitude = 3.85 + (53.55 - 3.85) * random.nextDouble();

            List<Double> centerCoords = Arrays.asList(
                Math.round(longitude * 1000000.0) / 1000000.0,
                Math.round(latitude * 1000000.0) / 1000000.0
            );
            coordinateVO.setCenter(centerCoords);
            coordinateVO.setCentroid(centerCoords);
        }

        return coordinateVO;
    }

    /**
     * 创建总部VO对象
     * @param config 地区配置
     * @return HeadquarterVO
     */
    private RegionCoordinateVO.HeadquarterVO createHeadquarterVO(RegionConfigDTO config) {
        RegionCoordinateVO.HeadquarterVO headquarterVO = new RegionCoordinateVO.HeadquarterVO();
        headquarterVO.setName(config.getHeadquarterName());
        headquarterVO.setHeadquarterCode(config.getHeadquarterCode());

        // 根据总部名称查找对应的省份坐标
        ProvinceCoordinateEnum province = ProvinceCoordinateEnum.fuzzyMatch(config.getHeadquarterName());

        if (province != null) {
            // 使用真实的省份坐标和行政区划代码
            headquarterVO.setAdcode(province.getAdcode());
            List<Double> centerCoords = Arrays.asList(province.getLongitude(), province.getLatitude());
            headquarterVO.setCenter(centerCoords);
            headquarterVO.setCentroid(centerCoords);
        } else {
            // 如果未找到匹配的省份，生成随机坐标作为备用
            Random random = new Random(config.getHeadquarterName().hashCode());
            String adcode = String.format("%06d", random.nextInt(999999));
            headquarterVO.setAdcode(adcode);

            // 生成中国范围内的随机坐标
            double longitude = 73.55 + (135.08 - 73.55) * random.nextDouble();
            double latitude = 3.85 + (53.55 - 3.85) * random.nextDouble();

            List<Double> centerCoords = Arrays.asList(
                Math.round(longitude * 1000000.0) / 1000000.0,
                Math.round(latitude * 1000000.0) / 1000000.0
            );
            headquarterVO.setCenter(centerCoords);
            headquarterVO.setCentroid(centerCoords);
        }

        return headquarterVO;
    }

    /**
     * 获取时段预约数据
     * @param dto 查询参数
     * @param type 数据类型 ("total"-总预约数, "car"-车险预约数)
     * @return 时段预约数据列表
     */
    private List<ReportHourlyVO> getHourlyReservationData(HourlyDto dto, String type) {
        List<ReportHourlyVO> result = new ArrayList<>();

        // 根据对比类型获取数据
        if ("0".equals(dto.getCompareType())) {
            // 小时对比
            result = getHourlyCompareData(dto, type);
        } else if ("1".equals(dto.getCompareType())) {
            // 天对比
            result = getDailyCompareData(dto, type);
        } else {
            // 默认获取当日时段数据
            result = getCurrentDayHourlyData(dto, type);
        }

        return result;
    }

    /**
     * 获取小时对比数据
     * @param dto 查询参数
     * @param type 数据类型
     * @return 时段数据列表
     */
    private List<ReportHourlyVO> getHourlyCompareData(HourlyDto dto, String type) {
        List<ReportHourlyVO> result = new ArrayList<>();

        // 生成24小时的时段数据
        for (int hour = 0; hour < 24; hour++) {
            ReportHourlyVO hourlyVO = new ReportHourlyVO();
            hourlyVO.setTimeFrame(String.format("%02d:00-%02d:00", hour, (hour + 1) % 24));

            // 模拟数据 - 实际应该从数据库查询
            Random random = new Random(dto.getOrderDate().hashCode() + hour);

            if ("total".equals(type)) {
                // 总预约数据
                int reservationNum = random.nextInt(50) + 10;
                hourlyVO.setReservationNum(String.valueOf(reservationNum));
            } else if ("car".equals(type)) {
                // 车险预约数据
                int carReservationNum = random.nextInt(30) + 5;
                hourlyVO.setReservationNum(String.valueOf(carReservationNum));
            }

            // 设置点击相关数据
            int clickNum = random.nextInt(100) + 20;
            hourlyVO.setClickNum(String.valueOf(clickNum));
            hourlyVO.setClickPv(String.valueOf(clickNum + random.nextInt(50)));

            // 计算转化率
            int reservationNum = Integer.parseInt(hourlyVO.getReservationNum());
            if (clickNum > 0) {
                BigDecimal rate = new BigDecimal(reservationNum)
                    .divide(new BigDecimal(clickNum), 2, RoundingMode.HALF_UP);
                hourlyVO.setReservationRate(rate);
            } else {
                hourlyVO.setReservationRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }

            result.add(hourlyVO);
        }

        return result;
    }

    /**
     * 获取天对比数据
     * @param dto 查询参数
     * @param type 数据类型
     * @return 时段数据列表
     */
    private List<ReportHourlyVO> getDailyCompareData(HourlyDto dto, String type) {
        List<ReportHourlyVO> result = new ArrayList<>();

        // 生成查询日期和对比日期的数据
        String[] dates = {dto.getOrderDate(), dto.getCompareDate()};

        for (String date : dates) {
            if (date != null) {
                ReportHourlyVO dailyVO = new ReportHourlyVO();
                dailyVO.setTimeFrame(date);

                // 模拟数据 - 实际应该从数据库查询
                Random random = new Random(date.hashCode());

                if ("total".equals(type)) {
                    // 总预约数据
                    int reservationNum = random.nextInt(500) + 100;
                    dailyVO.setReservationNum(String.valueOf(reservationNum));
                } else if ("car".equals(type)) {
                    // 车险预约数据
                    int carReservationNum = random.nextInt(300) + 50;
                    dailyVO.setReservationNum(String.valueOf(carReservationNum));
                }

                // 设置点击相关数据
                int clickNum = random.nextInt(1000) + 200;
                dailyVO.setClickNum(String.valueOf(clickNum));
                dailyVO.setClickPv(String.valueOf(clickNum + random.nextInt(500)));

                // 计算转化率
                int reservationNum = Integer.parseInt(dailyVO.getReservationNum());
                if (clickNum > 0) {
                    BigDecimal rate = new BigDecimal(reservationNum)
                        .divide(new BigDecimal(clickNum), 2, RoundingMode.HALF_UP);
                    dailyVO.setReservationRate(rate);
                } else {
                    dailyVO.setReservationRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }

                result.add(dailyVO);
            }
        }

        return result;
    }

    /**
     * 获取当日时段数据
     * @param dto 查询参数
     * @param type 数据类型
     * @return 时段数据列表
     */
    private List<ReportHourlyVO> getCurrentDayHourlyData(HourlyDto dto, String type) {
        // 默认返回小时对比数据
        return getHourlyCompareData(dto, type);
    }

    /**
     * 合并小时维度的点击数据和预约数据
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @return 合并后的结构化数据列表
     */
    private List<ReportStructureVO> mergeHourlyData(List<HourlyClickVO> clickDataList,
                                                   List<HourlyReservationVO> reservationDataList) {
        Map<String, ReportStructureVO> dataMap = new HashMap<>();

        // 处理点击数据
        for (HourlyClickVO clickData : clickDataList) {
            String key = generateKey(clickData.getHour(), clickData.getDataType());
            ReportStructureVO structureVO = dataMap.computeIfAbsent(key, k -> new ReportStructureVO());

            // 根据小时数生成时间段格式
            String timeFrame = formatTimeFrame(clickData.getHour());

            structureVO.setTimeFrame(timeFrame);
            structureVO.setClickPv(String.valueOf(clickData.getClickPv()));
            structureVO.setClickNum(String.valueOf(clickData.getClickNum()));
            structureVO.setDataType(clickData.getDataType());

            // 初始化预约数为0，后续会被预约数据覆盖
            if (structureVO.getReservationNum() == null) {
                structureVO.setReservationNum("0");
            }
        }

        // 处理预约数据
        for (HourlyReservationVO reservationData : reservationDataList) {
            String key = generateKey(reservationData.getHour(), reservationData.getDataType());
            ReportStructureVO structureVO = dataMap.computeIfAbsent(key, k -> new ReportStructureVO());

            // 如果点击数据还没设置，先设置基础信息
            if (structureVO.getTimeFrame() == null) {
                String timeFrame = formatTimeFrame(reservationData.getHour());

                structureVO.setTimeFrame(timeFrame);
                structureVO.setDataType(reservationData.getDataType());
                structureVO.setClickPv("0");
                structureVO.setClickNum("0");
            }

            structureVO.setReservationNum(String.valueOf(reservationData.getReservationNum()));
        }

        // 计算转化率并排序
        List<ReportStructureVO> result = new ArrayList<>(dataMap.values());
        for (ReportStructureVO vo : result) {
            calculateConversionRate(vo);
        }

        // 按小时和数据类型排序
        result.sort((a, b) -> {
            int hourA = extractHourFromTimeFrame(a.getTimeFrame());
            int hourB = extractHourFromTimeFrame(b.getTimeFrame());
            if (hourA != hourB) {
                return Integer.compare(hourA, hourB);
            }
            return a.getDataType().compareTo(b.getDataType());
        });

        return result;
    }

    /**
     * 生成数据合并的唯一键
     * @param hour 小时
     * @param dataType 数据类型
     * @return 唯一键
     */
    private String generateKey(Integer hour, String dataType) {
        return hour + "_" + dataType;
    }

    /**
     * 计算转化率
     * @param vo 结构化数据VO
     */
    private void calculateConversionRate(ReportStructureVO vo) {
        try {
            int clickNum = Integer.parseInt(vo.getClickNum());
            int reservationNum = Integer.parseInt(vo.getReservationNum());

            if (clickNum > 0) {
                BigDecimal rate = new BigDecimal(reservationNum)
                    .divide(new BigDecimal(clickNum), 2, RoundingMode.HALF_UP);
                vo.setReservationRate(rate);
            } else {
                vo.setReservationRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
        } catch (NumberFormatException e) {
            vo.setReservationRate(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 格式化时间段
     * @param hour 小时数 (0-23)
     * @return 格式化的时间段字符串，如"08:00-09:00"
     */
    private String formatTimeFrame(Integer hour) {
        if (hour == null) {
            return "00:00-01:00";
        }
        int nextHour = (hour + 1) % 24;
        return String.format("%02d:00-%02d:00", hour, nextHour);
    }

    /**
     * 从时间段字符串中提取小时数
     * @param timeFrame 时间段字符串，如"08:00-09:00"
     * @return 小时数
     */
    private int extractHourFromTimeFrame(String timeFrame) {
        try {
            if (timeFrame != null && timeFrame.contains(":")) {
                return Integer.parseInt(timeFrame.substring(0, 2));
            }
        } catch (NumberFormatException e) {
            // 忽略解析错误
        }
        return 0;
    }

    /**
     * 将ReportStructureVO列表转换为ReportHourlyVO列表
     * @param structureList 结构化数据列表
     * @param dataType 数据类型过滤 "0"-查询日期数据 "1"-对比日期数据
     * @return ReportHourlyVO列表
     */
    private List<ReportHourlyVO> convertToReportHourlyVO(List<ReportStructureVO> structureList, String dataType) {
        List<ReportHourlyVO> result = new ArrayList<>();

        for (ReportStructureVO structure : structureList) {
            // 只处理指定数据类型的数据
            if (dataType.equals(structure.getDataType())) {
                ReportHourlyVO hourlyVO = new ReportHourlyVO();
                hourlyVO.setTimeFrame(structure.getTimeFrame());
                hourlyVO.setClickPv(structure.getClickPv());
                hourlyVO.setClickNum(structure.getClickNum());
                hourlyVO.setReservationNum(structure.getReservationNum());
                hourlyVO.setReservationRate(structure.getReservationRate());

                result.add(hourlyVO);
            }
        }

        // 按小时排序
        result.sort((a, b) -> {
            int hourA = extractHourFromTimeFrame(a.getTimeFrame());
            int hourB = extractHourFromTimeFrame(b.getTimeFrame());
            return Integer.compare(hourA, hourB);
        });

        return result;
    }

    /**
     * 计算小时维度的环比数据
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @return 环比数据列表
     */
    private List<ReportHourlyListVO> calculateHourlyMonthOnMonthData(List<HourlyClickVO> clickDataList,
                                                                    List<HourlyReservationVO> reservationDataList) {
        // 按小时分组数据
        Map<Integer, Map<String, HourlyClickVO>> clickDataMap = groupClickDataByHour(clickDataList);
        Map<Integer, Map<String, HourlyReservationVO>> reservationDataMap = groupReservationDataByHour(reservationDataList);

        List<ReportHourlyListVO> result = new ArrayList<>();

        // 遍历0-23小时
        for (int hour = 0; hour < 24; hour++) {
            ReportHourlyListVO monthOnMonthVO = new ReportHourlyListVO();

            // 设置基础信息
            monthOnMonthVO.setTimeFrame(formatTimeFrame(hour));

            // 获取当期和基期的点击数据
            HourlyClickVO currentClickData = getDataByType(clickDataMap.get(hour), "0");
            HourlyClickVO baseClickData = getDataByType(clickDataMap.get(hour), "1");

            // 获取当期和基期的预约数据
            HourlyReservationVO currentReservationData = getDataByType(reservationDataMap.get(hour), "0");
            HourlyReservationVO baseReservationData = getDataByType(reservationDataMap.get(hour), "1");

            // 设置点击数据
            monthOnMonthVO.setCurrentClickPv(currentClickData != null ? currentClickData.getClickPv() : 0);
            monthOnMonthVO.setBaseClickPv(baseClickData != null ? baseClickData.getClickPv() : 0);
            monthOnMonthVO.setCurrentClickNum(currentClickData != null ? currentClickData.getClickNum() : 0);
            monthOnMonthVO.setBaseClickNum(baseClickData != null ? baseClickData.getClickNum() : 0);

            // 设置预约数据
            monthOnMonthVO.setCurrentReservationNum(currentReservationData != null ? currentReservationData.getReservationNum() : 0);
            monthOnMonthVO.setBaseReservationNum(baseReservationData != null ? baseReservationData.getReservationNum() : 0);

            // 计算环比增长率
            monthOnMonthVO.setClickPvGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentClickPv(), monthOnMonthVO.getBaseClickPv()));
            monthOnMonthVO.setClickNumGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentClickNum(), monthOnMonthVO.getBaseClickNum()));
            monthOnMonthVO.setReservationGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentReservationNum(), monthOnMonthVO.getBaseReservationNum()));

            // 计算转化率
            monthOnMonthVO.setCurrentConversionRate(calculateConversionRate(monthOnMonthVO.getCurrentReservationNum(), monthOnMonthVO.getCurrentClickNum()));
            monthOnMonthVO.setBaseConversionRate(calculateConversionRate(monthOnMonthVO.getBaseReservationNum(), monthOnMonthVO.getBaseClickNum()));

            result.add(monthOnMonthVO);
        }

        return result;
    }

    /**
     * 按小时分组点击数据
     * @param clickDataList 点击数据列表
     * @return 按小时分组的数据Map
     */
    private Map<Integer, Map<String, HourlyClickVO>> groupClickDataByHour(List<HourlyClickVO> clickDataList) {
        Map<Integer, Map<String, HourlyClickVO>> result = new HashMap<>();

        for (HourlyClickVO clickData : clickDataList) {
            result.computeIfAbsent(clickData.getHour(), k -> new HashMap<>())
                  .put(clickData.getDataType(), clickData);
        }

        return result;
    }

    /**
     * 按小时分组预约数据
     * @param reservationDataList 预约数据列表
     * @return 按小时分组的数据Map
     */
    private Map<Integer, Map<String, HourlyReservationVO>> groupReservationDataByHour(List<HourlyReservationVO> reservationDataList) {
        Map<Integer, Map<String, HourlyReservationVO>> result = new HashMap<>();

        for (HourlyReservationVO reservationData : reservationDataList) {
            result.computeIfAbsent(reservationData.getHour(), k -> new HashMap<>())
                  .put(reservationData.getDataType(), reservationData);
        }

        return result;
    }

    /**
     * 根据数据类型获取数据
     * @param dataMap 数据Map
     * @param dataType 数据类型
     * @return 对应类型的数据
     */
    private <T> T getDataByType(Map<String, T> dataMap, String dataType) {
        return dataMap != null ? dataMap.get(dataType) : null;
    }

    /**
     * 计算环比增长率
     * @param currentValue 当前值
     * @param compareValue 对比值
     * @return 增长率字符串
     */
    private String calculateGrowthRate(Integer currentValue, Integer compareValue) {
        if (compareValue == null || compareValue == 0) {
            return currentValue != null && currentValue > 0 ? "100.0" : "0.0";
        }

        if (currentValue == null) {
            return "-100.0";
        }

        double growthRate = ((double) (currentValue - compareValue) / compareValue) * 100;
        return String.format("%.1f", growthRate);
    }

    /**
     * 计算转化率
     * @param reservationNum 预约数
     * @param clickNum 点击数
     * @return 转化率字符串
     */
    private String calculateConversionRate(Integer reservationNum, Integer clickNum) {
        if (clickNum == null || clickNum == 0) {
            return "0.0";
        }

        if (reservationNum == null) {
            return "0.0";
        }

        double conversionRate = ((double) reservationNum / clickNum) * 100;
        return String.format("%.2f", conversionRate);
    }

    @Override
    public List<ProvinceStatisticsVO> getProvinceReservationStatistics(ProvinceStatisticsDto dto) {
        try {
            // 调用Mapper查询省份预约统计数据
            List<Map<String, Object>> rawResults = baseMapper.getProvinceReservationStatistics(dto);
            return convertToProvinceStatisticsVO(rawResults);
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("查询省份预约统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProvinceStatisticsVO> getCarInsuranceStatistics(ProvinceStatisticsDto dto) {
        try {
            // 调用Mapper查询车险统计数据
            List<Map<String, Object>> rawResults = baseMapper.getCarInsuranceStatistics(dto);
            return convertToProvinceStatisticsVO(rawResults);
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("查询车险统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProvinceStatisticsVO> getPropertyInsuranceStatistics(ProvinceStatisticsDto dto) {
        try {
            // 调用Mapper查询财险统计数据
            List<Map<String, Object>> rawResults = baseMapper.getPropertyInsuranceStatistics(dto);
            return convertToProvinceStatisticsVO(rawResults);
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("查询财险统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProvinceStatisticsVO> getValueAddedServiceStatistics(ProvinceStatisticsDto dto) {
        try {
            // 调用Mapper查询增值服务统计数据
            List<Map<String, Object>> rawResults = baseMapper.getValueAddedServiceStatistics(dto);
            return convertToProvinceStatisticsVO(rawResults);
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("查询增值服务统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将Map结果转换为ProvinceStatisticsVO，并通过ProvinceCoordinateEnum设置经纬度
     * @param rawResults 原始查询结果
     * @return 转换后的VO列表
     */
    private List<ProvinceStatisticsVO> convertToProvinceStatisticsVO(List<Map<String, Object>> rawResults) {
        List<ProvinceStatisticsVO> result = new ArrayList<>();

        for (Map<String, Object> row : rawResults) {
            ProvinceStatisticsVO vo = new ProvinceStatisticsVO();

            // 设置基本信息
            vo.setAdcode(String.valueOf(row.get("adcode")));
            vo.setName(String.valueOf(row.get("name")));
            vo.setReservationCount(Integer.valueOf(String.valueOf(row.get("reservationCount"))));

            // 通过城市名称从ProvinceCoordinateEnum获取经纬度
            String cityName = vo.getName();
            ProvinceCoordinateEnum province = ProvinceCoordinateEnum.fuzzyMatch(cityName);

            if (province != null) {
                // 使用枚举中的真实坐标数据
                List<Double> coords = Arrays.asList(province.getLongitude(), province.getLatitude());
                vo.setCenter(coords);
                vo.setCentroid(coords);
            } else {
                // 如果枚举中没有找到匹配的省份，设置默认坐标（北京）
                List<Double> defaultCoords = Arrays.asList(116.407526, 39.904030);
                vo.setCenter(defaultCoords);
                vo.setCentroid(defaultCoords);
            }

            result.add(vo);
        }

        return result;
    }

    @Override
    public List<ProvinceStatisticsVO> getProvinceStatisticsWithMultiThread(ProvinceStatisticsDto dto) {
        try {
            // 使用CompletableFuture进行多线程查询
            // 线程1：查询车险数据
            CompletableFuture<List<ProvinceStatisticsVO>> carInsuranceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getCarInsuranceStatistics(dto);
                } catch (Exception e) {
                    System.err.println("查询车险数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 线程2：查询财险数据
            CompletableFuture<List<ProvinceStatisticsVO>> propertyInsuranceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getPropertyInsuranceStatistics(dto);
                } catch (Exception e) {
                    System.err.println("查询财险数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 线程3：查询增值服务数据
            CompletableFuture<List<ProvinceStatisticsVO>> valueAddedServiceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getValueAddedServiceStatistics(dto);
                } catch (Exception e) {
                    System.err.println("查询增值服务数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 等待所有线程完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                carInsuranceFuture,
                propertyInsuranceFuture,
                valueAddedServiceFuture
            );

            // 等待所有查询完成
            allFutures.get();

            // 获取查询结果
            List<ProvinceStatisticsVO> carInsuranceList = carInsuranceFuture.get();
            List<ProvinceStatisticsVO> propertyInsuranceList = propertyInsuranceFuture.get();
            List<ProvinceStatisticsVO> valueAddedServiceList = valueAddedServiceFuture.get();

            // 合并三种类型的数据
            List<ProvinceStatisticsVO> resultList = mergeProvinceStatistics(
                carInsuranceList,
                propertyInsuranceList,
                valueAddedServiceList
            );

            return resultList;

        } catch (Exception e) {
            System.err.println("多线程查询省份统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 合并三种类型的省份统计数据
     * @param carInsuranceList 车险数据列表
     * @param propertyInsuranceList 财险数据列表
     * @param valueAddedServiceList 增值服务数据列表
     * @return 合并后的省份统计数据列表
     */
    private List<ProvinceStatisticsVO> mergeProvinceStatistics(
            List<ProvinceStatisticsVO> carInsuranceList,
            List<ProvinceStatisticsVO> propertyInsuranceList,
            List<ProvinceStatisticsVO> valueAddedServiceList) {

        // 使用Map来合并数据，key为adcode（省份编码）
        Map<String, ProvinceStatisticsVO> resultMap = new HashMap<>();

        // 处理车险数据
        for (ProvinceStatisticsVO vo : carInsuranceList) {
            ProvinceStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new ProvinceStatisticsVO(vo.getAdcode(), vo.getName()));
            result.setCenter(vo.getCenter());
            result.setCentroid(vo.getCentroid());
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 处理财险数据
        for (ProvinceStatisticsVO vo : propertyInsuranceList) {
            ProvinceStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new ProvinceStatisticsVO(vo.getAdcode(), vo.getName()));
            if (result.getCenter() == null) {
                result.setCenter(vo.getCenter());
                result.setCentroid(vo.getCentroid());
            }
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 处理增值服务数据
        for (ProvinceStatisticsVO vo : valueAddedServiceList) {
            ProvinceStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new ProvinceStatisticsVO(vo.getAdcode(), vo.getName()));
            if (result.getCenter() == null) {
                result.setCenter(vo.getCenter());
                result.setCentroid(vo.getCentroid());
            }
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 转换为List并按预约数量降序排序
        return resultMap.values().stream()
            .sorted((a, b) -> Integer.compare(b.getReservationCount(), a.getReservationCount()))
            .collect(Collectors.toList());
    }

    @Override
    public List<CityStatisticsVO> getCityStatisticsByProvinceCode(CityStatisticsDto dto) {
        try {
            // 使用CompletableFuture进行多线程查询
            // 线程1：查询车险数据
            CompletableFuture<List<CityStatisticsVO>> carInsuranceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getCityCarStats(dto);
                } catch (Exception e) {
                    System.err.println("查询城市车险数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 线程2：查询财险数据
            CompletableFuture<List<CityStatisticsVO>> propertyInsuranceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getCityPropertyStats(dto);
                } catch (Exception e) {
                    System.err.println("查询城市财险数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 线程3：查询增值服务数据
            CompletableFuture<List<CityStatisticsVO>> valueAddedServiceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return getCityValueAddedStats(dto);
                } catch (Exception e) {
                    System.err.println("查询城市增值服务数据失败: " + e.getMessage());
                    return new ArrayList<>();
                }
            }, executorService);

            // 等待所有线程完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                carInsuranceFuture,
                propertyInsuranceFuture,
                valueAddedServiceFuture
            );

            // 等待所有查询完成
            allFutures.get();

            // 获取查询结果
            List<CityStatisticsVO> carInsuranceList = carInsuranceFuture.get();
            List<CityStatisticsVO> propertyInsuranceList = propertyInsuranceFuture.get();
            List<CityStatisticsVO> valueAddedServiceList = valueAddedServiceFuture.get();

            // 合并三种类型的数据
            List<CityStatisticsVO> resultList = mergeCityStats(
                carInsuranceList,
                propertyInsuranceList,
                valueAddedServiceList
            );

            return resultList;

        } catch (Exception e) {
            System.err.println("多线程查询城市统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取城市车险预约统计数据
     * @param dto 查询参数
     * @return 城市车险统计数据列表
     */
    private List<CityStatisticsVO> getCityCarStats(CityStatisticsDto dto) {
        try {
            List<Map<String, Object>> rawResults = baseMapper.getCityCarInsuranceStatistics(dto);
            return convertToCityVO(rawResults);
        } catch (Exception e) {
            System.err.println("查询城市车险统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取城市财险预约统计数据
     * @param dto 查询参数
     * @return 城市财险统计数据列表
     */
    private List<CityStatisticsVO> getCityPropertyStats(CityStatisticsDto dto) {
        try {
            List<Map<String, Object>> rawResults = baseMapper.getCityPropertyInsuranceStatistics(dto);
            return convertToCityVO(rawResults);
        } catch (Exception e) {
            System.err.println("查询城市财险统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取城市增值服务预约统计数据
     * @param dto 查询参数
     * @return 城市增值服务统计数据列表
     */
    private List<CityStatisticsVO> getCityValueAddedStats(CityStatisticsDto dto) {
        try {
            List<Map<String, Object>> rawResults = baseMapper.getCityValueAddedServiceStatistics(dto);
            return convertToCityVO(rawResults);
        } catch (Exception e) {
            System.err.println("查询城市增值服务统计数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将Map结果转换为CityStatisticsVO，并通过ProvinceCoordinateEnum设置经纬度
     * @param rawResults 原始查询结果
     * @return 转换后的VO列表
     */
    private List<CityStatisticsVO> convertToCityVO(List<Map<String, Object>> rawResults) {
        List<CityStatisticsVO> result = new ArrayList<>();

        for (Map<String, Object> row : rawResults) {
            CityStatisticsVO vo = new CityStatisticsVO();

            // 设置基本信息
            vo.setAdcode(String.valueOf(row.get("adcode")));
            vo.setName(String.valueOf(row.get("name")));
            vo.setReservationCount(Integer.valueOf(String.valueOf(row.get("reservationCount"))));
            vo.setProvinceCode(String.valueOf(row.get("provinceCode")));
            vo.setProvinceName(String.valueOf(row.get("provinceName")));

            // 通过城市名称获取城市坐标
            List<Double> cityCoords = getCityCoordinatesByName(vo.getName());
            vo.setCenter(cityCoords);
            vo.setCentroid(cityCoords);

            result.add(vo);
        }

        return result;
    }

    /**
     * 根据城市名称获取城市坐标
     * @param cityName 城市名称
     * @return 城市坐标 [经度, 纬度]
     */
    private List<Double> getCityCoordinatesByName(String cityName) {
        if (cityName == null || cityName.trim().isEmpty()) {
            // 返回默认坐标（北京）
            return Arrays.asList(116.407526, 39.904030);
        }

        // 遍历所有省份，查找匹配的城市
        for (ProvinceCoordinateEnum province : ProvinceCoordinateEnum.values()) {
            for (ProvinceCoordinateEnum.CityCoordinate city : province.getCities()) {
                // 精确匹配城市名称
                if (city.getCityName().equals(cityName)) {
                    return Arrays.asList(city.getLongitude(), city.getLatitude());
                }

                // 模糊匹配：去掉"市"、"区"、"县"等后缀
                String cleanCityName = cityName.replace("市", "").replace("区", "").replace("县", "");
                String cleanEnumCityName = city.getCityName().replace("市", "").replace("区", "").replace("县", "");

                if (cleanEnumCityName.equals(cleanCityName) ||
                    cleanEnumCityName.contains(cleanCityName) ||
                    cleanCityName.contains(cleanEnumCityName)) {
                    return Arrays.asList(city.getLongitude(), city.getLatitude());
                }
            }
        }

        // 如果没有找到匹配的城市，尝试通过省份名称匹配（作为备选方案）
        ProvinceCoordinateEnum province = ProvinceCoordinateEnum.fuzzyMatch(cityName);
        if (province != null) {
            return Arrays.asList(province.getLongitude(), province.getLatitude());
        }

        // 最终备选：返回默认坐标（北京）
        return Arrays.asList(116.407526, 39.904030);
    }

    /**
     * 合并三种类型的城市统计数据
     * @param carInsuranceList 车险数据列表
     * @param propertyInsuranceList 财险数据列表
     * @param valueAddedServiceList 增值服务数据列表
     * @return 合并后的城市统计数据列表
     */
    private List<CityStatisticsVO> mergeCityStats(
            List<CityStatisticsVO> carInsuranceList,
            List<CityStatisticsVO> propertyInsuranceList,
            List<CityStatisticsVO> valueAddedServiceList) {

        // 使用Map来合并数据，key为adcode（城市编码）
        Map<String, CityStatisticsVO> resultMap = new HashMap<>();

        // 处理车险数据
        for (CityStatisticsVO vo : carInsuranceList) {
            CityStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new CityStatisticsVO(vo.getAdcode(), vo.getName(), vo.getProvinceCode(), vo.getProvinceName()));
            result.setCenter(vo.getCenter());
            result.setCentroid(vo.getCentroid());
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 处理财险数据
        for (CityStatisticsVO vo : propertyInsuranceList) {
            CityStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new CityStatisticsVO(vo.getAdcode(), vo.getName(), vo.getProvinceCode(), vo.getProvinceName()));
            if (result.getCenter() == null) {
                result.setCenter(vo.getCenter());
                result.setCentroid(vo.getCentroid());
            }
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 处理增值服务数据
        for (CityStatisticsVO vo : valueAddedServiceList) {
            CityStatisticsVO result = resultMap.computeIfAbsent(vo.getAdcode(),
                k -> new CityStatisticsVO(vo.getAdcode(), vo.getName(), vo.getProvinceCode(), vo.getProvinceName()));
            if (result.getCenter() == null) {
                result.setCenter(vo.getCenter());
                result.setCentroid(vo.getCentroid());
            }
            result.setReservationCount(result.getReservationCount() + vo.getReservationCount());
        }

        // 转换为List并按预约数量降序排序
        return resultMap.values().stream()
            .sorted((a, b) -> Integer.compare(b.getReservationCount(), a.getReservationCount()))
            .collect(Collectors.toList());
    }

    /**
     * 合并周期维度的点击数据和预约数据
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @param compareType 对比类型
     * @return 合并后的结构化数据列表
     */
    private List<ReportStructureVO> mergePeriodData(List<HourlyClickVO> clickDataList,
                                                   List<HourlyReservationVO> reservationDataList,
                                                   String compareType) {
        Map<String, ReportStructureVO> dataMap = new HashMap<>();

        // 处理点击数据
        for (HourlyClickVO clickData : clickDataList) {
            String key = generatePeriodKey(clickData.getPeriodValue(), clickData.getDataType());
            ReportStructureVO structureVO = dataMap.computeIfAbsent(key, k -> new ReportStructureVO());

            // 使用periodValue作为时间段显示
            String timeFrame = clickData.getPeriodValue() != null ? clickData.getPeriodValue() : clickData.getStatDate();

            structureVO.setTimeFrame(timeFrame);
            structureVO.setClickPv(String.valueOf(clickData.getClickPv()));
            structureVO.setClickNum(String.valueOf(clickData.getClickNum()));
            structureVO.setDataType(clickData.getDataType());

            // 初始化预约数为0，后续会被预约数据覆盖
            if (structureVO.getReservationNum() == null) {
                structureVO.setReservationNum("0");
            }
        }

        // 处理预约数据
        for (HourlyReservationVO reservationData : reservationDataList) {
            String key = generatePeriodKey(reservationData.getPeriodValue(), reservationData.getDataType());
            ReportStructureVO structureVO = dataMap.computeIfAbsent(key, k -> new ReportStructureVO());

            // 如果点击数据还没设置，先设置基础信息
            if (structureVO.getTimeFrame() == null) {
                String timeFrame = reservationData.getPeriodValue() != null ? reservationData.getPeriodValue() : reservationData.getStatDate();

                structureVO.setTimeFrame(timeFrame);
                structureVO.setDataType(reservationData.getDataType());
                structureVO.setClickPv("0");
                structureVO.setClickNum("0");
            }

            structureVO.setReservationNum(String.valueOf(reservationData.getReservationNum()));
        }

        // 计算转化率并排序
        List<ReportStructureVO> result = new ArrayList<>(dataMap.values());
        for (ReportStructureVO vo : result) {
            calculateConversionRate(vo);
        }

        // 按时间和数据类型排序
        result.sort((a, b) -> {
            // 先按数据类型排序
            int typeCompare = a.getDataType().compareTo(b.getDataType());
            if (typeCompare != 0) {
                return typeCompare;
            }
            // 再按时间排序
            return a.getTimeFrame().compareTo(b.getTimeFrame());
        });

        return result;
    }

    /**
     * 生成周期数据合并的唯一键
     * @param periodValue 周期值
     * @param dataType 数据类型
     * @return 唯一键
     */
    private String generatePeriodKey(Object periodValue, String dataType) {
        return String.valueOf(periodValue) + "_" + dataType;
    }

    /**
     * 根据对比类型格式化时间段
     * @param compareType 对比类型
     * @param timeValue 时间值
     * @return 格式化的时间段字符串
     */
    private String formatTimeFrameByType(String compareType, Object timeValue) {
        if (timeValue == null) {
            return "";
        }

        switch (compareType) {
            case "1": // 天
                return String.valueOf(timeValue);
            case "2": // 周
                return String.valueOf(timeValue);
            case "3": // 月
                return String.valueOf(timeValue);
            default:
                return String.valueOf(timeValue);
        }
    }

    /**
     * 计算周期维度的环比数据
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @param compareType 对比类型
     * @return 环比数据列表
     */
    private List<ReportHourlyListVO> calculatePeriodMonthOnMonthData(List<HourlyClickVO> clickDataList,
                                                                    List<HourlyReservationVO> reservationDataList,
                                                                    String compareType) {
        // 按周期分组数据
        Map<Object, Map<String, HourlyClickVO>> clickDataMap = groupClickDataByPeriod(clickDataList);
        Map<Object, Map<String, HourlyReservationVO>> reservationDataMap = groupReservationDataByPeriod(reservationDataList);

        List<ReportHourlyListVO> result = new ArrayList<>();

        // 获取所有周期值
        Set<Object> allPeriods = new HashSet<>();
        allPeriods.addAll(clickDataMap.keySet());
        allPeriods.addAll(reservationDataMap.keySet());

        for (Object period : allPeriods) {
            ReportHourlyListVO monthOnMonthVO = new ReportHourlyListVO();

            // 设置基础信息
            monthOnMonthVO.setTimeFrame(String.valueOf(period));

            // 获取当期和基期的点击数据
            HourlyClickVO currentClickData = getDataByType(clickDataMap.get(period), "0");
            HourlyClickVO baseClickData = getDataByType(clickDataMap.get(period), "1");

            // 获取当期和基期的预约数据
            HourlyReservationVO currentReservationData = getDataByType(reservationDataMap.get(period), "0");
            HourlyReservationVO baseReservationData = getDataByType(reservationDataMap.get(period), "1");

            // 设置点击数据
            monthOnMonthVO.setCurrentClickPv(currentClickData != null ? currentClickData.getClickPv() : 0);
            monthOnMonthVO.setBaseClickPv(baseClickData != null ? baseClickData.getClickPv() : 0);
            monthOnMonthVO.setCurrentClickNum(currentClickData != null ? currentClickData.getClickNum() : 0);
            monthOnMonthVO.setBaseClickNum(baseClickData != null ? baseClickData.getClickNum() : 0);

            // 设置预约数据
            monthOnMonthVO.setCurrentReservationNum(currentReservationData != null ? currentReservationData.getReservationNum() : 0);
            monthOnMonthVO.setBaseReservationNum(baseReservationData != null ? baseReservationData.getReservationNum() : 0);

            // 计算环比增长率
            monthOnMonthVO.setClickPvGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentClickPv(), monthOnMonthVO.getBaseClickPv()));
            monthOnMonthVO.setClickNumGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentClickNum(), monthOnMonthVO.getBaseClickNum()));
            monthOnMonthVO.setReservationGrowthRate(calculateGrowthRate(monthOnMonthVO.getCurrentReservationNum(), monthOnMonthVO.getBaseReservationNum()));

            // 计算转化率
            monthOnMonthVO.setCurrentConversionRate(calculateConversionRate(monthOnMonthVO.getCurrentReservationNum(), monthOnMonthVO.getCurrentClickNum()));
            monthOnMonthVO.setBaseConversionRate(calculateConversionRate(monthOnMonthVO.getBaseReservationNum(), monthOnMonthVO.getBaseClickNum()));

            result.add(monthOnMonthVO);
        }

        // 按时间排序
        result.sort((a, b) -> a.getTimeFrame().compareTo(b.getTimeFrame()));

        return result;
    }

    /**
     * 按周期分组点击数据
     * @param clickDataList 点击数据列表
     * @return 按周期分组的数据Map
     */
    private Map<Object, Map<String, HourlyClickVO>> groupClickDataByPeriod(List<HourlyClickVO> clickDataList) {
        Map<Object, Map<String, HourlyClickVO>> result = new HashMap<>();

        for (HourlyClickVO clickData : clickDataList) {
            Object periodKey = clickData.getPeriodValue() != null ? clickData.getPeriodValue() : clickData.getStatDate();
            result.computeIfAbsent(periodKey, k -> new HashMap<>())
                  .put(clickData.getDataType(), clickData);
        }

        return result;
    }

    /**
     * 按周期分组预约数据
     * @param reservationDataList 预约数据列表
     * @return 按周期分组的数据Map
     */
    private Map<Object, Map<String, HourlyReservationVO>> groupReservationDataByPeriod(List<HourlyReservationVO> reservationDataList) {
        Map<Object, Map<String, HourlyReservationVO>> result = new HashMap<>();

        for (HourlyReservationVO reservationData : reservationDataList) {
            Object periodKey = reservationData.getPeriodValue() != null ? reservationData.getPeriodValue() : reservationData.getStatDate();
            result.computeIfAbsent(periodKey, k -> new HashMap<>())
                  .put(reservationData.getDataType(), reservationData);
        }

        return result;
    }

    /**
     * 按周汇总点击数据
     * @param dailyClickDataList 按天的点击数据列表
     * @return 按周汇总的点击数据列表
     */
    private List<HourlyClickVO> aggregateClickDataByWeek(List<HourlyClickVO> dailyClickDataList) {
        Map<String, HourlyClickVO> weeklyDataMap = new HashMap<>();

        for (HourlyClickVO dailyData : dailyClickDataList) {
            // 计算周的开始日期（周一）
            String weekKey = getWeekKey(dailyData.getPeriodValue(), dailyData.getDataType());

            HourlyClickVO weeklyData = weeklyDataMap.computeIfAbsent(weekKey, k -> {
                HourlyClickVO newWeeklyData = new HourlyClickVO();
                String weekStart = getWeekStartDate(dailyData.getPeriodValue());
                newWeeklyData.setHour(null);  // 周汇总时hour为null
                newWeeklyData.setStatDate(weekStart);
                newWeeklyData.setDataType(dailyData.getDataType());
                newWeeklyData.setClickPv(0);
                newWeeklyData.setClickNum(0);
                newWeeklyData.setPeriodValue(getWeekDisplayName(weekStart));  // 设置周显示名称
                return newWeeklyData;
            });

            // 累加数据
            weeklyData.setClickPv(weeklyData.getClickPv() + dailyData.getClickPv());
            weeklyData.setClickNum(weeklyData.getClickNum() + dailyData.getClickNum());
        }

        return new ArrayList<>(weeklyDataMap.values());
    }

    /**
     * 按周汇总预约数据
     * @param dailyReservationDataList 按天的预约数据列表
     * @return 按周汇总的预约数据列表
     */
    private List<HourlyReservationVO> aggregateReservationDataByWeek(List<HourlyReservationVO> dailyReservationDataList) {
        Map<String, HourlyReservationVO> weeklyDataMap = new HashMap<>();

        for (HourlyReservationVO dailyData : dailyReservationDataList) {
            // 计算周的开始日期（周一）
            String weekKey = getWeekKey(dailyData.getPeriodValue(), dailyData.getDataType());

            HourlyReservationVO weeklyData = weeklyDataMap.computeIfAbsent(weekKey, k -> {
                HourlyReservationVO newWeeklyData = new HourlyReservationVO();
                String weekStart = getWeekStartDate(dailyData.getPeriodValue());
                newWeeklyData.setHour(null);  // 周汇总时hour为null
                newWeeklyData.setStatDate(weekStart);
                newWeeklyData.setDataType(dailyData.getDataType());
                newWeeklyData.setReservationNum(0);
                newWeeklyData.setPeriodValue(getWeekDisplayName(weekStart));  // 设置周显示名称
                return newWeeklyData;
            });

            // 累加数据
            weeklyData.setReservationNum(weeklyData.getReservationNum() + dailyData.getReservationNum());
        }

        return new ArrayList<>(weeklyDataMap.values());
    }

    /**
     * 按月汇总点击数据
     * @param dailyClickDataList 按天的点击数据列表
     * @return 按月汇总的点击数据列表
     */
    private List<HourlyClickVO> aggregateClickDataByMonth(List<HourlyClickVO> dailyClickDataList) {
        Map<String, HourlyClickVO> monthlyDataMap = new HashMap<>();

        for (HourlyClickVO dailyData : dailyClickDataList) {
            // 计算月份
            String monthKey = getMonthKey(dailyData.getPeriodValue(), dailyData.getDataType());

            HourlyClickVO monthlyData = monthlyDataMap.computeIfAbsent(monthKey, k -> {
                HourlyClickVO newMonthlyData = new HourlyClickVO();
                String monthStart = getMonthStartDate(dailyData.getPeriodValue());
                newMonthlyData.setHour(null);  // 月汇总时hour为null
                newMonthlyData.setStatDate(monthStart);
                newMonthlyData.setDataType(dailyData.getDataType());
                newMonthlyData.setClickPv(0);
                newMonthlyData.setClickNum(0);
                newMonthlyData.setPeriodValue(getMonthDisplayName(monthStart));  // 设置月显示名称
                return newMonthlyData;
            });

            // 累加数据
            monthlyData.setClickPv(monthlyData.getClickPv() + dailyData.getClickPv());
            monthlyData.setClickNum(monthlyData.getClickNum() + dailyData.getClickNum());
        }

        return new ArrayList<>(monthlyDataMap.values());
    }

    /**
     * 按月汇总预约数据
     * @param dailyReservationDataList 按天的预约数据列表
     * @return 按月汇总的预约数据列表
     */
    private List<HourlyReservationVO> aggregateReservationDataByMonth(List<HourlyReservationVO> dailyReservationDataList) {
        Map<String, HourlyReservationVO> monthlyDataMap = new HashMap<>();

        for (HourlyReservationVO dailyData : dailyReservationDataList) {
            // 计算月份
            String monthKey = getMonthKey(dailyData.getPeriodValue(), dailyData.getDataType());

            HourlyReservationVO monthlyData = monthlyDataMap.computeIfAbsent(monthKey, k -> {
                HourlyReservationVO newMonthlyData = new HourlyReservationVO();
                String monthStart = getMonthStartDate(dailyData.getPeriodValue());
                newMonthlyData.setHour(null);  // 月汇总时hour为null
                newMonthlyData.setStatDate(monthStart);
                newMonthlyData.setDataType(dailyData.getDataType());
                newMonthlyData.setReservationNum(0);
                newMonthlyData.setPeriodValue(getMonthDisplayName(monthStart));  // 设置月显示名称
                return newMonthlyData;
            });

            // 累加数据
            monthlyData.setReservationNum(monthlyData.getReservationNum() + dailyData.getReservationNum());
        }

        return new ArrayList<>(monthlyDataMap.values());
    }

    /**
     * 获取周的唯一键
     * @param statDate 统计日期
     * @param dataType 数据类型
     * @return 周的唯一键
     */
    private String getWeekKey(Object statDate, String dataType) {
        String weekStart = getWeekStartDate(statDate);
        return weekStart + "_" + dataType;
    }

    /**
     * 获取月的唯一键
     * @param statDate 统计日期
     * @param dataType 数据类型
     * @return 月的唯一键
     */
    private String getMonthKey(Object statDate, String dataType) {
        String monthStart = getMonthStartDate(statDate);
        return monthStart + "_" + dataType;
    }

    /**
     * 获取周的开始日期（周一）
     * @param statDate 统计日期
     * @return 周开始日期字符串
     */
    private String getWeekStartDate(Object statDate) {
        try {
            LocalDate date = LocalDate.parse(statDate.toString());
            LocalDate weekStart = date.with(DayOfWeek.MONDAY);
            return weekStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return statDate.toString();
        }
    }

    /**
     * 获取月的开始日期
     * @param statDate 统计日期
     * @return 月开始日期字符串
     */
    private String getMonthStartDate(Object statDate) {
        try {
            LocalDate date = LocalDate.parse(statDate.toString());
            LocalDate monthStart = date.withDayOfMonth(1);
            return monthStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return statDate.toString();
        }
    }

    /**
     * 获取周的显示名称
     * @param weekStartDate 周开始日期
     * @return 周显示名称，如"2025年第24周"
     */
    private String getWeekDisplayName(String weekStartDate) {
        try {
            LocalDate date = LocalDate.parse(weekStartDate);
            int year = date.getYear();
            int weekOfYear = date.getDayOfYear() / 7 + 1;
            return year + "年第" + weekOfYear + "周";
        } catch (Exception e) {
            return weekStartDate;
        }
    }

    /**
     * 获取月的显示名称
     * @param monthStartDate 月开始日期
     * @return 月显示名称，如"2025-06"
     */
    private String getMonthDisplayName(String monthStartDate) {
        try {
            LocalDate date = LocalDate.parse(monthStartDate);
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        } catch (Exception e) {
            return monthStartDate;
        }
    }

    /**
     * 计算汇总数据（周期维度）
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @return 汇总数据
     */
    private ReportSummaryVO calculateSummaryData(List<HourlyClickVO> clickDataList,
                                                List<HourlyReservationVO> reservationDataList) {
        // 只统计查询期间的数据（dataType = "0"）
        int totalVisitCount = clickDataList.stream()
            .filter(data -> "0".equals(data.getDataType()))
            .mapToInt(data -> data.getClickNum() != null ? data.getClickNum() : 0)
            .sum();

        int totalReservationCount = reservationDataList.stream()
            .filter(data -> "0".equals(data.getDataType()))
            .mapToInt(data -> data.getReservationNum() != null ? data.getReservationNum() : 0)
            .sum();

        return new ReportSummaryVO(totalVisitCount, totalReservationCount);
    }









    /**
     * 计算小时同比数据
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @return 小时同比数据列表
     */
    private List<ReportHourlyListVO> calculateHourlyYearOverYearData(List<HourlyClickVO> clickDataList,
                                                                    List<HourlyReservationVO> reservationDataList) {
        // 复用现有的小时环比计算逻辑，但语义上是同比
        return calculateHourlyMonthOnMonthData(clickDataList, reservationDataList);
    }

    /**
     * 计算周期同比数据（天/周/月）
     * @param clickDataList 点击数据列表
     * @param reservationDataList 预约数据列表
     * @param compareType 对比类型
     * @return 周期同比数据列表
     */
    private List<ReportHourlyListVO> calculatePeriodYearOverYearData(List<HourlyClickVO> clickDataList,
                                                                    List<HourlyReservationVO> reservationDataList,
                                                                    String compareType) {
        List<ReportHourlyListVO> result = new ArrayList<>();

        // 按时间段分组数据
        Map<Object, Map<String, HourlyClickVO>> clickDataMap = groupClickDataByPeriod(clickDataList);
        Map<Object, Map<String, HourlyReservationVO>> reservationDataMap = groupReservationDataByPeriod(reservationDataList);

        // 获取所有时间段
        Set<Object> allPeriods = new HashSet<>();
        allPeriods.addAll(clickDataMap.keySet());
        allPeriods.addAll(reservationDataMap.keySet());

        for (Object period : allPeriods) {
            Map<String, HourlyClickVO> clickMap = clickDataMap.get(period);
            Map<String, HourlyReservationVO> reservationMap = reservationDataMap.get(period);

            // 获取查询期间和对比期间的数据
            HourlyClickVO currentClickData = clickMap != null ? clickMap.get("0") : null;
            HourlyClickVO baseClickData = clickMap != null ? clickMap.get("1") : null;
            HourlyReservationVO currentReservationData = reservationMap != null ? reservationMap.get("0") : null;
            HourlyReservationVO baseReservationData = reservationMap != null ? reservationMap.get("1") : null;

            if (currentClickData != null || currentReservationData != null) {
                ReportHourlyListVO yearOverYearVO = new ReportHourlyListVO();

                // 设置时间显示
                String timeFrame = currentClickData != null ?
                    (currentClickData.getPeriodValue() != null ? currentClickData.getPeriodValue() : currentClickData.getStatDate()) :
                    (currentReservationData != null ?
                        (currentReservationData.getPeriodValue() != null ? currentReservationData.getPeriodValue() : currentReservationData.getStatDate()) : "");
                yearOverYearVO.setTimeFrame(timeFrame);

                // 设置点击数据
                yearOverYearVO.setCurrentClickPv(currentClickData != null ? currentClickData.getClickPv() : 0);
                yearOverYearVO.setBaseClickPv(baseClickData != null ? baseClickData.getClickPv() : 0);
                yearOverYearVO.setCurrentClickNum(currentClickData != null ? currentClickData.getClickNum() : 0);
                yearOverYearVO.setBaseClickNum(baseClickData != null ? baseClickData.getClickNum() : 0);

                // 设置预约数据
                yearOverYearVO.setCurrentReservationNum(currentReservationData != null ? currentReservationData.getReservationNum() : 0);
                yearOverYearVO.setBaseReservationNum(baseReservationData != null ? baseReservationData.getReservationNum() : 0);

                // 计算同比增长率
                yearOverYearVO.setClickPvGrowthRate(calculateGrowthRate(yearOverYearVO.getCurrentClickPv(), yearOverYearVO.getBaseClickPv()));
                yearOverYearVO.setClickNumGrowthRate(calculateGrowthRate(yearOverYearVO.getCurrentClickNum(), yearOverYearVO.getBaseClickNum()));
                yearOverYearVO.setReservationGrowthRate(calculateGrowthRate(yearOverYearVO.getCurrentReservationNum(), yearOverYearVO.getBaseReservationNum()));

                // 计算转化率
                yearOverYearVO.setCurrentConversionRate(calculateConversionRate(yearOverYearVO.getCurrentReservationNum(), yearOverYearVO.getCurrentClickNum()));
                yearOverYearVO.setBaseConversionRate(calculateConversionRate(yearOverYearVO.getBaseReservationNum(), yearOverYearVO.getBaseClickNum()));

                result.add(yearOverYearVO);
            }
        }

        return result;
    }


}