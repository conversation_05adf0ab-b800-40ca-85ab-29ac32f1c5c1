package org.jeecg.modules.corp.controller;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import org.jeecg.modules.corp.dto.DailyConfigContent;
import org.jeecg.modules.corp.dto.DailyConfigDto;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.service.IClickAutoPreService;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.info.util.ChinaPlateNumberGenerator;
import org.jeecg.config.util.LicensePlateToProvince;
import org.jeecg.modules.corp.dto.ChatDto;
import org.jeecg.modules.corp.dto.DeleteBatchDTO;
import org.jeecg.modules.corp.dto.DeleteBatchRequestDTO;
import org.jeecg.modules.corp.dto.DeleteByTypeDTO;
import org.jeecg.modules.corp.dto.LedgerImportDTO;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdCarInfoRel;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.corp.service.IPdCarInfoRelService;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.corp.service.IPdLedgerService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.corp.util.GenerateRecords;
import org.jeecg.modules.corp.util.RandomCarBrandGenerator;
import org.jeecg.modules.corp.util.RandomInsuranceTypeGenerator;
import org.jeecg.modules.corp.util.RandomVinGenerator;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 台账
 * @Author: jeecg-boot
 * @Date:   2024-11-08
 * @Version: V1.0
 */
@Api(tags="台账")
@RestController
@RequestMapping("/corp/pdLedger")
@Slf4j
public class PdLedgerController extends JeecgController<PdLedger, IPdLedgerService> {
	@Autowired
	private IPdLedgerService pdLedgerService;
	 @Autowired
	 private IPdIntegratedService pdIntegratedService;
	 @Autowired
	 private IPdCarInfoService carInfoService;
	 @Autowired
	 private IPdCarInfoRelService carInfoRelService;
	 @Autowired
	 private IPdChatService pdChatService;
	 @Autowired
	 private IPdChatUserService pdChatUserService;
	 @Autowired
	 private IPdGuestUsersService guestUsersService;
	 @Autowired
	 private GenerateRecords generateRecordUtil;
	 @Autowired
	 private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private IClickAutoPreService clickAutoPreService;

	 @Autowired
	 private ObjectMapper objectMapper;

	 private static final String CHAT_QUEUE = "chatQueue";




	/**
	 * 分页列表查询
	 *
	 * @return
	 */
	//@AutoLog(value = "台账-分页列表查询")
	@ApiOperation(value="台账-分页列表查询", notes="台账-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdLedger>> queryPageList(@PathVariable(name = "pageNum")Long pageNum,
												 @PathVariable(name = "pageSize")Long pageSize,
												 @RequestBody LedgerListDto dto) {
		Page<PdLedger> page = new Page<>(pageNum, pageSize);
		IPage<PdLedger> pageList = pdLedgerService.pageList(page,dto);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param pdLedger
	 * @return
	 */
	@AutoLog(value = "台账-添加")
	@ApiOperation(value="台账-添加", notes="台账-添加")
	@RequiresPermissions("corp:pd_ledger:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdLedger pdLedger) {
		pdLedgerService.save(pdLedger);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdLedger
	 * @return
	 */
	@AutoLog(value = "台账-编辑")
	@ApiOperation(value="台账-编辑", notes="台账-编辑")
	@RequiresPermissions("corp:pd_ledger:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdLedger pdLedger) {
		pdLedgerService.updateById(pdLedger);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "台账-通过id删除")
	@ApiOperation(value="台账-通过id删除", notes="台账-通过id删除")
	@RequiresPermissions("corp:pd_ledger:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdLedgerService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @return
	 */
	@AutoLog(value = "台账-批量删除")
	@ApiOperation(value = "台账-批量删除", notes = "台账-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	@Transactional(rollbackFor = Exception.class)
	public Result<String> deleteBatch(@RequestBody DeleteBatchDTO dto) {
		try {
			List<String> ledgerIds = dto.getIds();
			if (ledgerIds.isEmpty()) {
				return Result.OK("没有需要删除的台账记录！");
			}

			// 1. 查询关联的 PdCarInfo
			List<PdCarInfo> carInfoList = carInfoService.lambdaQuery()
					.in(PdCarInfo::getLedgerId, ledgerIds).list();

			if (carInfoList.isEmpty()) {
				// 没有关联的 PdCarInfo，直接删除 PdLedger
				pdLedgerService.removeByIds(ledgerIds);
				return Result.OK("批量删除成功！");
			}

			List<String> carInfoIds = carInfoList.stream()
					.map(PdCarInfo::getId)
					.filter(Objects::nonNull)
					.distinct()
					.collect(Collectors.toList());

			List<String> guestIds = carInfoList.stream()
					.map(PdCarInfo::getGuestId)
					.filter(Objects::nonNull)
					.distinct()
					.collect(Collectors.toList());

			// 2. 删除聊天记录 (PdChat, 根据 userId = guestId)
			if (!guestIds.isEmpty()) {
				LambdaQueryWrapper<PdChat> chatQuery = new LambdaQueryWrapper<>();
				chatQuery.in(PdChat::getUserId, guestIds);
				pdChatService.remove(chatQuery);
			}

			// 3. 删除 PdCarInfoRel (根据 carInfoId)
			if (!carInfoIds.isEmpty()) {
				LambdaQueryWrapper<PdCarInfoRel> relQuery = new LambdaQueryWrapper<>();
				relQuery.in(PdCarInfoRel::getCarInfoId, carInfoIds);
				carInfoRelService.remove(relQuery);
			}

			// 4. 删除聊天用户 (PdChatUser, 根据 keyId = ledgerId)
			if (!ledgerIds.isEmpty()) {
				LambdaQueryWrapper<PdChatUser> chatUserQuery = new LambdaQueryWrapper<>();
				chatUserQuery.in(PdChatUser::getKeyId, ledgerIds);
				pdChatUserService.remove(chatUserQuery);
			}

			// 5. 删除 PdCarInfo (根据 carInfoId)
			if (!carInfoIds.isEmpty()) {
				carInfoService.removeByIds(carInfoIds);
			}

			// 6. 删除 PdGuestUsers (根据 guestId)
			if (!guestIds.isEmpty()) {
				guestUsersService.removeByIds(guestIds);
			}

			// 7. 删除 PdLedger
			pdLedgerService.removeByIds(ledgerIds);

			return Result.OK("批量删除成功！删除记录数: " + ledgerIds.size());

		} catch (Exception e) {
			log.error("批量删除失败: ", e);
			return Result.error("批量删除失败: " + e.getMessage());
		}
	}

	 @PostMapping(value = "/deleteBatchs")
	 @Transactional(rollbackFor = Exception.class)
	 public Result<String> deleteBatchs(@RequestBody DeleteBatchRequestDTO dto) {
		 try {
			 // 创建DTO并调用新方法
			 DeleteByTypeDTO deleteDto = new DeleteByTypeDTO();
			 deleteDto.setType(0); // 车险类型
			 deleteDto.setTenantIds(dto.getTenantIdList()); // 单个租户ID转为列表
			 deleteDto.setStartDate(dto.getStartDate());
			 deleteDto.setEndDate(dto.getEndDate());


			 // 调用服务方法执行删除
			 pdLedgerService.deleteByTypeAndDateRange(deleteDto);


			 return Result.OK("批量删除成功! 删除记录数: ");
		 } catch (Exception e) {
			 return Result.error("批量删除失败: " + e.getMessage());
		 }
	 }

	 /**
	  * 根据类型和时间范围批量删除数据
	  *
	  * @param dto 删除参数DTO
	  * @return 删除结果
	  */
	 @DeleteMapping(value = "/deleteByType")
	 @Transactional(rollbackFor = Exception.class)
	 public Result<String> deleteByType(@RequestBody DeleteByTypeDTO dto) {
		 try {
			 // 验证参数
			 if (dto.getType() == null || dto.getType() < 0 || dto.getType() > 2) {
				 return Result.error("删除类型参数错误，有效值为：0-车险，1-财险，2-增值服务");
			 }

			 if (dto.getStartDate() == null || dto.getEndDate() == null) {
				 return Result.error("开始日期和结束日期不能为空");
			 }

			 if (dto.getStartDate().isAfter(dto.getEndDate())) {
				 return Result.error("开始日期不能晚于结束日期");
			 }

			 if (dto.getTenantIds() == null || dto.getTenantIds().isEmpty()) {
				 return Result.error("租户ID列表不能为空");
			 }

			 // 记录删除前的数量
			 int beforeCount = 0;
			 for (String tenantId : dto.getTenantIds()) {
				 beforeCount += pdLedgerService.count(new LambdaQueryWrapper<PdLedger>()
						 .eq(PdLedger::getTenantId, tenantId)
						 .between(PdLedger::getSignDate,
								 dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
								 dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
			 }

			 // 调用服务方法执行删除
			 pdLedgerService.deleteByTypeAndDateRange(dto);

			 if (beforeCount == 0) {
				 return Result.OK("没有符合条件的记录需要删除！");
			 }

			 return Result.OK("批量删除成功! 删除记录数: " + beforeCount);
		 } catch (Exception e) {
			 return Result.error("批量删除失败: " + e.getMessage());
		 }
	 }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "台账-通过id查询")
	@ApiOperation(value="台账-通过id查询", notes="台账-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdLedger> queryById(@RequestParam(name="id",required=true) String id) {
		PdLedger pdLedger = pdLedgerService.getById(id);
		if(pdLedger==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdLedger);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdLedger
    */
    @RequiresPermissions("corp:pd_ledger:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdLedger pdLedger) {
		// Step.1 组装查询条件
		QueryWrapper<PdLedger> queryWrapper = new QueryWrapper<>();
		// Step.1 获取请求参数
		String tenantId = request.getParameter("tenantId");
		if (StringUtils.isNotBlank(tenantId)) {
			queryWrapper.eq("tenant_id", tenantId);
		}
		String dateRange = request.getParameter("dateRange");
		if (StringUtils.isNotBlank(dateRange)) {
			String[] dates = dateRange.split(",");
			if (dates.length == 2) {
				String startDate = dates[0].trim();
				String endDate = dates[1].trim();
				queryWrapper.ge("sign_date", startDate ); // 起始时间
				queryWrapper.le("sign_date", endDate ); // 结束时间
			}
		}

		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 过滤选中数据
		String selections = request.getParameter("selections");
		if (oConvertUtils.isNotEmpty(selections)) {
			List<String> selectionList = Arrays.asList(selections.split(","));
			queryWrapper.in("id",selectionList);
		}
		// Step.2 获取导出数据
		List<PdLedger> exportList = service.list(queryWrapper);

		// Step.3 AutoPoi 导出Excel
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		//此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, "台账");
		mv.addObject(NormalExcelConstants.CLASS, PdLedger.class);
		//update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
		ExportParams exportParams=new ExportParams("台账" + "报表", "导出人:" + sysUser.getRealname(), "台账");
		//update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
		mv.addObject(NormalExcelConstants.PARAMS,exportParams);
		mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
		return mv;
    }
	 @RequestMapping(value = "/pageSystemCompletion", method = RequestMethod.POST)
	 @ApiOperation(value="系统补全", notes="系统补全")
	 public Result<?> pageSystemCompletion(@RequestBody LedgerImportDTO dto) {
		 try {
			 // 验证必要参数
			 if (dto.getTenantId() == null || dto.getTenantId().isEmpty()) {
				 return Result.error("租户ID列表不能为空");
			 }

			 if (dto.getStartDate() == null || dto.getEndDate() == null) {
				 return Result.error("开始日期和结束日期不能为空");
			 }

			 if (dto.getClickNum() == null || dto.getClickNum() <= 0) {
				 return Result.error("点击数必须大于0");
			 }

			 if (dto.getConfigJson() == null || dto.getConfigJson().isEmpty()) {
				 return Result.error("配置JSON不能为空");
			 }

			 // 解析configJson
			 DailyConfigContent configContent;
			 try {
				 configContent = objectMapper.readValue(dto.getConfigJson(), DailyConfigContent.class);
			 } catch (Exception e) {
				 log.error("解析configJson失败: {}", e.getMessage());
				 return Result.error("配置JSON格式错误: " + e.getMessage());
			 }

			 // 生成唯一任务ID
			 String taskId = UUID.randomUUID().toString();

			 // 为每个租户创建一个DailyConfigDto并存入Redis
			 String redisKey = "tenant_batch_process:configs";
			 // 清空之前的配置（如果有）
			 redisTemplate.delete(redisKey);

			 // 处理每个租户
			 for (String tenantIdStr : dto.getTenantId()) {
				 try {
					 // 创建DailyConfigDto对象
					 DailyConfigDto dailyConfigDto = new DailyConfigDto();
					 dailyConfigDto.setTenantId(Integer.parseInt(tenantIdStr));

					 // 设置日期范围
					 DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
					 LocalDate startDate = LocalDate.parse(dto.getStartDate(), formatter);
					 LocalDate endDate = LocalDate.parse(dto.getEndDate(), formatter);
					 dailyConfigDto.setMonthDataStart(startDate);
					 dailyConfigDto.setMonthDataEnd(endDate);

					 // 设置点击数和链接类型
					 dailyConfigDto.setClickNum(dto.getClickNum());
					 dailyConfigDto.setLinkType(dto.getLinkType());

					 // 设置配置JSON
					 dailyConfigDto.setConfigJson(dto.getConfigJson());

					 // 将DailyConfigDto对象转为JSON字符串并存入Redis
					 String configJson = objectMapper.writeValueAsString(dailyConfigDto);
					 redisTemplate.opsForList().rightPush(redisKey, configJson);

					 log.info("租户 {} 的配置已添加到处理队列", tenantIdStr);
				 } catch (Exception e) {
					 log.error("处理租户 {} 配置失败: {}", tenantIdStr, e.getMessage());
				 }
			 }

			 // 记录初始任务总数
			 Long totalTasks = redisTemplate.opsForList().size(redisKey);
			 redisTemplate.opsForValue().set("tenant_batch_process:total_tasks", totalTasks.toString());

			 // 启动异步处理任务
			 clickAutoPreService.processTenantConfigsAsync();

			 // 设置Redis过期时间到明天凌晨1点
			 Calendar tomorrow = Calendar.getInstance();
			 tomorrow.add(Calendar.DAY_OF_MONTH, 1);
			 tomorrow.set(Calendar.HOUR_OF_DAY, 1);
			 tomorrow.set(Calendar.MINUTE, 0);
			 tomorrow.set(Calendar.SECOND, 0);

			 // 计算从现在到明天凌晨1点的秒数
			 long expireSeconds = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

			 // 设置Redis过期时间
			 redisTemplate.expire(redisKey, expireSeconds, TimeUnit.SECONDS);
			 redisTemplate.expire("tenant_batch_process:total_tasks", expireSeconds, TimeUnit.SECONDS);

			 return Result.OK("批量补全任务已创建");
		 } catch (Exception e) {
			 log.error("导入失败", e);
			 return Result.error("导入失败：" + e.getMessage());
		 }
	 }

	 private void licensePlateProvince(List<PdLedger> list, String provinces) {
		 if (list == null || list.isEmpty() || provinces == null || provinces.trim().isEmpty()) {
			 return;
		 }

		 // 解析 provinces JSON 字符串为 cityList
		 List<String> cityList = new ArrayList<>();
		 try {
			 JSONArray array = JSON.parseArray(provinces);
			 for (int i = 0; i < array.size(); i++) {
				 JSONObject obj = array.getJSONObject(i);
				 String city = obj.getString("city");
				 if ( StringUtils.isNotEmpty(city)) {
					 cityList.add(city.trim());
				 }
			 }
		 } catch (Exception e) {
			 e.printStackTrace();
			 return;
		 }

		 if (cityList.isEmpty()) {
			 return;
		 }

		 for (PdLedger ledger : list) {
			 // 张健  导入系统后变为张先生   郑敏王导入后变为郑女士
			 String insured = ChinaPlateNumberGenerator.getGenderTitle(ledger.getInsured());
			 ledger.setInsured(insured).setPolicyholder(insured);
			 String licensePlate = null;

			 // 随机选择一个 city
			 String selectedCity = cityList.get(new Random().nextInt(cityList.size()));

			 for (int i = 0; i < 3; i++) {
				 // 传入 city 参数生成车牌
				 licensePlate = ChinaPlateNumberGenerator.generatePlateNumber(selectedCity);

				 Long count = this.pdLedgerService.lambdaQuery()
						 .eq(PdLedger::getLicensePlate, licensePlate)
						 .last("limit 1")
						 .count();

				 if (count == 0) {
					 break;
				 }
			 }

			 // 传入 city 参数生成城市字段
			 String city = LicensePlateToProvince.getCityByProvinces(selectedCity);
			 if (StringUtils.isNotEmpty(city)) {
				 ledger.setCity(city);
			 }

			 ledger.setLicensePlate(licensePlate);
		 }
	 }



	 /**
	  * Process data for import type 1 (use original Excel data)
	  */
	 private void processImportType1(List<PdLedger> list, SimpleDateFormat dateFormat) {
		 for (PdLedger pdLedger : list) {
			 try {
				 String signDate = pdLedger.getSignDate();
				 Date createTime;
				 if (signDate != null && !signDate.isEmpty()) {
					 if (signDate.matches("\\d+")) {
						 long excelDays = Long.parseLong(signDate);
						 long millis = (excelDays - 25569L) * 24 * 60 * 60 * 1000;
						 createTime = new Date(millis);
					 } else {
						 createTime = dateFormat.parse(signDate);
					 }
				 } else {
					 createTime = new Date();
				 }
				 pdLedger.setSignDateTime(createTime);
				 pdLedger.setSignDate(dateFormat.format(createTime));
			 } catch (Exception e) {
				 log.error("日期解析失败: {}", pdLedger.getSignDate(), e);
				 Date fallbackDate = new Date();
				 pdLedger.setSignDateTime(fallbackDate);
				 pdLedger.setSignDate(dateFormat.format(fallbackDate));
			 }
		 }
	 }

	 /**
	  * Process data for import type 2 (data cleaning + random generation)
	  * @return filtered list of valid entries
	  */
	 private List<PdLedger> processImportType2(List<PdLedger> validList, LocalDate startLocalDate, LocalDate endLocalDate) {
		 // Filter valid records first

		 if (validList.isEmpty()) {
			 return Collections.emptyList();
		 }

		 // Calculate days between
		 long daysBetween = ChronoUnit.DAYS.between(startLocalDate, endLocalDate) + 1;
		 int recordCount = validList.size();

		 // Create date range list
		 List<LocalDate> dateRange = new ArrayList<>();
		 for (long i = 0; i < daysBetween; i++) {
			 dateRange.add(startLocalDate.plusDays(i));
		 }

		 // Prepare all records with random data
		 for (PdLedger pdLedger : validList) {
			 if (pdLedger.getInsuranceName() == null) {
				 pdLedger.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
			 }
			 if (pdLedger.getBrandModel() == null) {
				 pdLedger.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
			 }
			 if (pdLedger.getVin() == null) {
				 pdLedger.setVin(RandomVinGenerator.generateRandomVin());
			 }

			 if (StrUtil.isEmpty(pdLedger.getPhoneNumber()) || StrUtil.isBlank(pdLedger.getPhoneNumber())) {
				 pdLedger.setPhoneNumber(ProvinceIpGenerator.generateMaskedPhone());
			 }
		 }

		 // Distribute dates according to logic
		 if (recordCount < daysBetween) {
			 // Scenario 1: Records < Days
			 processRecordsLessThanDays(validList, dateRange, recordCount);
		 } else {
			 // Scenario 2: Records >= Days
			 processRecordsMoreThanDays(validList, dateRange, recordCount);
		 }

		 return validList;
	 }

	 /**
	  * Process scenario where record count is less than days
	  */
	 private void processRecordsLessThanDays(List<PdLedger> validList, List<LocalDate> dateRange, int recordCount) {
		 // Randomly select dates without replacement
		 Collections.shuffle(dateRange);
		 List<LocalDate> selectedDates = dateRange.subList(0, recordCount);

		 // Assign dates to records
		 for (int i = 0; i < recordCount; i++) {
			 PdLedger pdLedger = validList.get(i);
			 String insured = pdLedger.getInsured();
			 String licensePlate = pdLedger.getLicensePlate();

			 LocalDate randomDate = selectedDates.get(i);
			 assignDateToPdLedger(pdLedger, randomDate, insured, licensePlate);
		 }
	 }

	 /**
	  * Process scenario where record count is greater than or equal to days
	  */
	 private void processRecordsMoreThanDays(List<PdLedger> validList, List<LocalDate> dateRange, int recordCount) {
		 Map<LocalDate, Integer> dateRecordCount = new HashMap<>();
		 for (LocalDate date : dateRange) {
			 dateRecordCount.put(date, 0);
		 }

		 // First round: ensure at least one record per day
		 for (int i = 0; i < dateRange.size(); i++) {
			 LocalDate date = dateRange.get(i);
			 PdLedger pdLedger = validList.get(i);
			 String insured = pdLedger.getInsured();
			 String licensePlate = pdLedger.getLicensePlate();

			 assignDateToPdLedger(pdLedger, date, insured, licensePlate);
			 dateRecordCount.put(date, 1);
		 }

		 // Second round: distribute remaining records with weighting
		 distributeRemainingRecords(validList, dateRange, dateRecordCount, recordCount);
	 }

	 /**
	  * Distribute remaining records with weighted randomization
	  */
	 private void distributeRemainingRecords(List<PdLedger> validList, List<LocalDate> dateRange,
											 Map<LocalDate, Integer> dateRecordCount, int recordCount) {
		 Random random = new Random();
		 int avgRecordsPerDay = recordCount / dateRange.size();

		 for (int i = dateRange.size(); i < recordCount; i++) {
			 PdLedger pdLedger = validList.get(i);
			 String insured = pdLedger.getInsured();
			 String licensePlate = pdLedger.getLicensePlate();

			 // Create weighted date selection
			 List<LocalDate> weightedDates = new ArrayList<>();
			 for (Map.Entry<LocalDate, Integer> entry : dateRecordCount.entrySet()) {
				 int weight = avgRecordsPerDay - entry.getValue() + 1;
				 for (int j = 0; j < Math.max(1, weight); j++) {
					 weightedDates.add(entry.getKey());
				 }
			 }

			 // Select random date based on weight
			 LocalDate randomDate = weightedDates.get(random.nextInt(weightedDates.size()));
			 assignDateToPdLedger(pdLedger, randomDate, insured, licensePlate);
			 dateRecordCount.put(randomDate, dateRecordCount.get(randomDate) + 1);
		 }
	 }

	 /**
	  * Prepare car info and relationship records efficiently
	  */
	 private void prepareCarInfoAndRelationships(List<PdLedger> list, List<PdCarInfo> carInfoList,
												 List<PdCarInfoRel> allCarInfoRelList,
												 List<PdIntegrated> integratedList,
												 Random random) {
		 // Process each record
		 for (int i = 0; i < list.size(); i++) {
			 PdLedger pdLedger = list.get(i);
			 String licensePlate = pdLedger.getLicensePlate();
			 if (licensePlate == null || licensePlate.isEmpty()) {
				 continue;
			 }

			 // Create car info with efficient data generation
			 PdCarInfo carInfo = createCarInfo(pdLedger, random);
			 carInfoList.add(carInfo);


		 }
	 }

	 /**
	  * Create a car info record with efficient data generation
	  */

	 private PdCarInfo createCarInfo(PdLedger pdLedger, Random random) {
		 PdCarInfo pdCarInfo = new PdCarInfo();

		 String insured = pdLedger.getInsured();
		 String sex;

		 if (StringUtils.isNotBlank(insured)) {
			 if (insured.contains("先生")) {
				 sex = "男";
			 } else if (insured.contains("女士")) {
				 sex = "女";
			 } else {
				 sex = random.nextInt(2) == 0 ? "男" : "女";
			 }
		 } else {
			 sex = random.nextInt(2) == 0 ? "男" : "女";
		 }

		 pdCarInfo.setSex(sex);
		 String licensePlate = pdLedger.getLicensePlate();
		 String address = LicensePlateToProvince.getProvinceFromLicensePlate(licensePlate.substring(0, 1));
		 String city = LicensePlateToProvince.getRandomCity(address);
		 if (StrUtil.isNotEmpty(pdLedger.getCity())) {
			 city = pdLedger.getCity();
		 }
		 String ipForProvince = ProvinceIpGenerator.getRandomIpForProvince(address);
		 Date date = generateRandomCreateTime(pdLedger.getSignDateTime());

		 return
				 pdCarInfo.setCity(city)
				 .setModel(pdLedger.getBrandModel())
				 .setLicensePlateNumber(licensePlate)
				 .setVinCode(pdLedger.getVin())
				 .setOwner(pdLedger.getInsured())
				 .setIpAddress(ipForProvince)
				 .setLedgerId(pdLedger.getId())
				 .setCreateTime(date)
				 .setLicensePlateNumber(pdLedger.getLicensePlate())
				 .setPhoneNumber(pdLedger.getPhoneNumber())

				 .setTenantId(pdLedger.getTenantId());
	 }

	 private void assignDateToPdLedger(PdLedger pdLedger, LocalDate date, String insured, String licensePlate) {
		 String signDateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		 pdLedger.setSignDate(signDateStr).setPolicyholder(pdLedger.getInsured());
		 pdLedger.setSignDateTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		 pdLedger.setInsured(insured);
		 pdLedger.setLicensePlate(licensePlate);
	 }

	 // Method 2: Generate chat records for all PdLedger entries
	 @RequestMapping(value = "/chatGiveTo", method = RequestMethod.POST)
	 public void generateChatRecords() {
		 try {
			 // 查询所有未生成车险台账数据
			 List<PdLedger> ledgerList = pdLedgerService.lambdaQuery().eq(PdLedger::getChatStatus, 0).eq(PdLedger::getIsDelete, 0).list();
			 if (ledgerList.isEmpty()) {
				 log.info("未找到未生成车险台账数据");
				 return ;
			 }

			 long start = System.currentTimeMillis();
			 int successCount = 0;
			 int failCount = 0;
			 List<PdLedger> updateList = new ArrayList<>();

			 for (PdLedger pdLedger : ledgerList) {
				 // 查询对应的 carInfo 和 guestUser
				 LambdaQueryWrapper<PdCarInfo> carInfoQuery = new LambdaQueryWrapper<>();
				 carInfoQuery.eq(PdCarInfo::getLedgerId, pdLedger.getId());
				 PdCarInfo carInfo = carInfoService.getOne(carInfoQuery);

				 if (carInfo == null) {
					 log.error("未找到对应的车辆信息: LedgerId={}", pdLedger.getId());
					 failCount++;
					 continue;
				 }

				 // 获取游客用户
				 PdGuestUsers guestUser = guestUsersService.getById(carInfo.getGuestId());
				 if (guestUser == null) {
					 log.error("未找到对应的游客用户: GuestId={}", carInfo.getGuestId());
					 failCount++;
					 continue;
				 }

				 // 查找对应的保险服务
				 LambdaQueryWrapper<PdIntegrated> chatQueryWrapper = new LambdaQueryWrapper<>();
				 chatQueryWrapper.eq(PdIntegrated::getType, 0)
						 .eq(PdIntegrated::getName, pdLedger.getInsuranceName())
						 .last(" limit 1");
				 PdIntegrated integrated = pdIntegratedService.getOne(chatQueryWrapper);

				 if (integrated == null) {
					 log.error("未找到对应的服务：" + pdLedger.getInsuranceName());
					 failCount++;
					 continue;
				 }

				 // 生成随机IP
				 String ipForChat = ProvinceIpGenerator.getRandomIpForProvinceByRandom();
				 Date date = carInfo.getCreateTime();

				 try {
					 ChatDto chatDto = new ChatDto();
					 chatDto.setIp(ipForChat)
							 .setUser(guestUser)
							 .setLinkType(0)
							 .setTenantId(pdLedger.getTenantId())
							 .setUserItem(pdLedger.getInsuranceName())
							 .setKid(pdLedger.getId())
							 .setDate(date);
					 // 将对象转换为 JSON 字符串后再存入 Redis
					 String chatDtoJson = objectMapper.writeValueAsString(chatDto);
					 redisTemplate.opsForList().rightPush(CHAT_QUEUE, chatDtoJson);

					 //generateRecordUtil.processChatRecords(chatDto);
					 successCount++;
					 PdLedger update = new PdLedger();
					 update.setId(pdLedger.getId()).setChatStatus(1);
					 updateList.add(update);
				 } catch (Exception e) {
					 log.error("聊天记录生成失败: LedgerId={}", pdLedger.getId(), e);
					 failCount++;
				 }
			 }
			 if (!updateList.isEmpty()) {
				 pdLedgerService.saveOrUpdateBatch(updateList);
			 }

			 //log.info("聊天记录生成完成，耗时: {} 毫秒，成功: {}，失败: {}",
					 //System.currentTimeMillis() - start, successCount, failCount);
			 return ;
		 } catch (Exception e) {
			 log.error("聊天记录生成失败", e);
			 return ;
		 }
	 }

	 private Date generateRandomCreateTime(Date signDateTime) {
		 Random random = new Random();
		 Calendar calendar = Calendar.getInstance();
		 calendar.setTime(signDateTime); // 基于 signDateTime 的日期

		 // 5% 概率落在 0-6 点，其余 95% 落在 6-24 点
		 int hour;
		 if (random.nextDouble() < 0.05) {
			 hour = random.nextInt(6); // 0-5 点
		 } else {
			 hour = 6 + random.nextInt(18); // 6-23 点（23:59:59 仍有效）
		 }

		 int minute = random.nextInt(60); // 0-59 分
		 int second = random.nextInt(60); // 0-59 秒

		 // 设置随机时间
		 calendar.set(Calendar.HOUR_OF_DAY, hour);
		 calendar.set(Calendar.MINUTE, minute);
		 calendar.set(Calendar.SECOND, second);
		 calendar.set(Calendar.MILLISECOND, 0); // 毫秒清零，避免额外误差

		 return calendar.getTime();
	 }


 }
