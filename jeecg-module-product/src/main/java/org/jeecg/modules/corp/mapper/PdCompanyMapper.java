package org.jeecg.modules.corp.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.entity.PdCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: app 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
public interface PdCompanyMapper extends BaseMapper<PdCompany> {

    /**
     * 根据保司ID列表查询保司信息（包含已删除和是否展示条件）
     * @param companyIds 保司ID列表
     * @return 保司列表
     */
    List<PdCompany> queryByCompanyIds(@Param("companyIds") List<String> companyIds);

    /**
     * 查询所有保司信息（包含已删除和是否展示条件）
     * @return 保司列表
     */
    List<PdCompany> queryAllCompanies();

}
