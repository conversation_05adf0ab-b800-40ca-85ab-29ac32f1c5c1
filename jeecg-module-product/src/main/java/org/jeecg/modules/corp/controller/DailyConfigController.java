package org.jeecg.modules.corp.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.corp.dto.DailyConfigDto;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.jeecg.modules.corp.service.IDailyConfigService;
import org.jeecg.modules.corp.vo.BatchImportResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
* <p>
 * 每日租户配置表前端控制器
 * </p>
*
* <AUTHOR>
* @since 2025-04-12
*/
@Api(tags = "每日租户配置表")
@RestController
@Slf4j
@RequestMapping("daily-config")
public class DailyConfigController {
    @Autowired
    private IDailyConfigService iDailyConfigService;

    @ApiOperation("新增每日租户配置表")
    @PostMapping("/add")
    public Result<DailyConfig> addDailyConfig(@Valid @RequestBody DailyConfig dto) {
        return Result.OK(iDailyConfigService.add(dto));
    }

    @ApiOperation("修改每日租户配置表")
    @PostMapping("/edit/{id}")
    @ApiImplicitParam(value = "每日租户配置表id",name = "id",dataTypeClass = String.class)
    public Result<DailyConfig> editDailyConfig(@PathVariable String id, @Valid @RequestBody DailyConfig dto) {
        dto.setId(id);
        return Result.OK(iDailyConfigService.edit(dto));
    }

   @ApiOperation("根据租户id获取每日租户配置表详情")
   @GetMapping("info/{tenantId}")
   @ApiImplicitParam(value = "租户id",name = "tenantId",dataTypeClass = Integer.class)
   public Result<DailyConfig> queryDailyConfigById(@PathVariable Integer tenantId) {
        return Result.OK(iDailyConfigService.queryById(tenantId));
    }

    @ApiOperation(value="验证导入文件")
    @PostMapping(value = {"/validateFile/multi"})
    public Result<BatchImportResultVO> validateMultipleFile(@RequestPart("file") MultipartFile file) throws Exception {
        BatchImportResultVO result = iDailyConfigService.validateImportFile(file);
        return Result.OK(result);
    }

    @ApiOperation(value="导入配置")
    @PostMapping(value = {"/uploadFile/multi"})
    public Result<BatchImportResultVO> uploadMultipleFile(@RequestPart("file") MultipartFile file) throws Exception {
        BatchImportResultVO result = iDailyConfigService.uploadMultipleFileWithValidation(file);
        return Result.OK(result);
    }

}