package org.jeecg.modules.corp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

/**
 * 台账导入参数 DTO
 */
@Data
@ApiModel(value = "台账导入参数", description = "台账导入参数")
public class LedgerImportDTO {

    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    @ApiModelProperty(value = "点击数")
    private Integer clickNum;

    @ApiModelProperty("每日配置 JSON 内容（包含：台账区间、聊天用户区间、城市范围、充值日期，消耗金额，单价）")
    private String configJson;

    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private Integer linkType;

    @ApiModelProperty(value = "多租户id")
    private List<String> tenantId;
}
