package org.jeecg.modules.corp.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.dto.DailyConfigContent;
import org.jeecg.modules.corp.dto.DailyConfigExcelDTO;
import org.jeecg.modules.corp.dto.PdInsuranceLedgerDTO;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.jeecg.modules.corp.mapper.DailyConfigDao;
import org.jeecg.modules.corp.service.IDailyConfigService;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.jeecg.modules.corp.util.ExcelUtils;
import org.jeecg.modules.corp.vo.BatchImportResultVO;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 每日租户配置表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
@Service
@Slf4j
public class DailyConfigServiceImpl extends ServiceImpl<DailyConfigDao, DailyConfig> implements IDailyConfigService {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private IEaRegionService eaRegionService;
    @Autowired
    private ISysCityPlatePrefixService sysCityPlatePrefixService;

    /**
    * 新增每日租户配置表
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DailyConfig add(DailyConfig dto){
        dto.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        save(dto);
        return dto;
    }

    /**
    * 修改每日租户配置表
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DailyConfig edit(DailyConfig dto){
        dto.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        updateById(dto);
        return dto;
    }


    /**
    * 根据id获取每日租户配置表 详情
    *
    * @param tenantId 主键
    */
    @Override
    public DailyConfig queryById(Integer tenantId){
        return lambdaQuery().eq(DailyConfig::getTenantId, tenantId).last("limit 1").one();
    }

    @Override
    public void uploadMultipleFile(MultipartFile file) throws Exception {

        List<DailyConfig> dailyConfigList = new ArrayList<>();
        // 2. 读取 Excel 内容，使用 ExcelImportUtil 进行导入
        InputStream inputStream = file.getInputStream();
        // 设置导入参数：跳过标题行，标题行行数为2行
        ImportParams params = new ImportParams();
        params.setTitleRows(2);  // 跳过前两行（标题行）
        params.setHeadRows(1);   // 第一行作为头行
        params.setNeedSave(true);

        // 读取 Excel 内容为 List<DailyConfigExcelDTO> 类型
        List<DailyConfigExcelDTO> rowList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);


        if (rowList.isEmpty()) {
            throw new IOException("导入的 Excel 文件没有数据");
        }

        List<Integer> tenantIdList = rowList.stream().map(DailyConfigExcelDTO::getTenantId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tenantIdList)){
            return;
        }
        this.lambdaUpdate()
                .in(DailyConfig::getTenantId, tenantIdList)
                .remove();

        for (DailyConfigExcelDTO row : rowList) {
            DailyConfig config = new DailyConfig();
            config.setClickStart(row.getDailyClickStart());
            config.setClickEnd(row.getDailyClickEnd());
            config.setTenantId(row.getTenantId());

            Map<String, Object> configJson = new HashMap<>();
            if (row.getCarInsuranceSwitch()!=0) {
                // 车险
                Map<String, Double> carLedger = new HashMap<>();
                carLedger.put("ledgerStart", row.getCarInsuranceStart());
                carLedger.put("ledgerEnd", row.getCarInsuranceEnd());
                carLedger.put("chatUserStart", row.getChatStart());
                carLedger.put("chatUserEnd", row.getChatEnd());
                configJson.put("carLedger", carLedger);
            }

            if (row.getPropertyInsuranceSwitch()!=0) {
                // 财险
                Map<String, Double> financeLedger = new HashMap<>();
                financeLedger.put("ledgerStart", row.getPropertyInsuranceStart());
                financeLedger.put("ledgerEnd", row.getPropertyInsuranceEnd());
                financeLedger.put("chatUserStart", row.getPropertyChatStart());
                financeLedger.put("chatUserEnd", row.getPropertyChatEnd());
                configJson.put("financeLedger", financeLedger);
            }

            if (row.getValueServiceSwitch()!=0) {
                // 增值服务
                Map<String, Double> valueAddedLedger = new HashMap<>();
                valueAddedLedger.put("ledgerStart", row.getValueServiceStart());
                valueAddedLedger.put("ledgerEnd", row.getValueServiceEnd());
                valueAddedLedger.put("chatUserStart", row.getValueChatStart());
                valueAddedLedger.put("chatUserEnd", row.getValueChatEnd());
                configJson.put("valueAddedLedger", valueAddedLedger);
            }
            // 将逗号分隔的城市字符串转换为List<String>类型，存储城市编码
            List<String> cityList = new ArrayList<>();
            // 城市配置
            if (row.getCities() != null && !row.getCities().isEmpty()) {
                // 按逗号分割城市名称
                String[] cityNames = row.getCities().split(",");


                // 遍历每个城市名称，查询对应的城市编码
                for (String cityName : cityNames) {
                    // 去除可能的空格
                    cityName = cityName.trim();
                    if (!cityName.isEmpty()) {
                        // 根据城市名称模糊查询城市信息
                        List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);

                        // 如果找到匹配的城市，添加其编码到列表中
                        if (regions != null && !regions.isEmpty()) {
                            // 取第一个匹配的城市编码
                            String cityCode = regions.get(0).getCode();
                            cityList.add(cityCode);
                            log.info("城市 [{}] 匹配到编码: {}", cityName, cityCode);
                        } else {
                            log.warn("城市 [{}] 未找到匹配的编码", cityName);
                        }
                    }
                }
                configJson.put("cityList", cityList);
            }
            // 城市比例配置 - 使用与ClickAutoPreServiceImpl一致的逻辑
            List<DailyConfigContent.CityRatio> cityRatioList = new ArrayList<>();
            if (row.getCityRatios() != null && !row.getCityRatios().isEmpty()) {
                // 按逗号分割城市比例
                String[] cityRatios = row.getCityRatios().split(",");
                for (int i = 0; i < cityRatios.length && i < cityList.size(); i++) {
                    String cityRatio = cityRatios[i].trim();
                    if (!cityRatio.isEmpty()) {
                        try {
                            String cityCode = cityList.get(i);
                            Double ratio = Double.parseDouble(cityRatio);

                            // 创建城市比例对象
                            DailyConfigContent.CityRatio cityRatioObj = new DailyConfigContent.CityRatio();
                            cityRatioObj.setCityCode(cityCode);
                            cityRatioObj.setRatio(ratio);
                            cityRatioList.add(cityRatioObj);
                            log.info("Excel导入(简单) - 城市编码: {} 比例: {}%", cityCode, ratio);
                        } catch (NumberFormatException e) {
                            log.warn("Excel导入(简单) - 城市比例格式错误: {}", cityRatio);
                        }
                    }
                }
            }

            // 使用formatCity方法处理一级城市编码，将其转换为二级城市编码并分配比例
            DailyConfigContent formattedCityConfig = eaRegionService.formatCity(cityRatioList);
            JSONArray objects = new JSONArray();
            if (formattedCityConfig != null && formattedCityConfig.getCityRatios() != null) {
                for (DailyConfigContent.CityRatio cityRatio : formattedCityConfig.getCityRatios()) {
                    Map<String, Object> cityRatioMap = new HashMap<>();
                    cityRatioMap.put("cityCode", cityRatio.getCityCode());
                    cityRatioMap.put("ratio", cityRatio.getRatio());
                    objects.add(cityRatioMap);
                }
                log.info("Excel导入(简单) - 城市比例格式化完成，一级城市已转换为二级城市，共 {} 个城市比例配置", formattedCityConfig.getCityRatios().size());
            }
            configJson.put("cityRatios", objects);
            config.setConfigJson(JSON.toJSONString(configJson));
            dailyConfigList.add(config);

        }
        if (!dailyConfigList.isEmpty()) {
            saveBatch(dailyConfigList);
        }

    }

    @Override
    public BatchImportResultVO validateImportFile(MultipartFile file) throws Exception {
        List<BatchImportResultVO.BatchImportFailureDetail> failureDetails = new ArrayList<>();

        // 读取 Excel 内容
        InputStream inputStream = file.getInputStream();
        ImportParams params = new ImportParams();
        params.setTitleRows(2);  // 跳过前两行（标题行）
        params.setHeadRows(1);   // 第一行作为头行
        params.setNeedSave(true);

        List<DailyConfigExcelDTO> rowList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);

        if (rowList.isEmpty()) {
            throw new IOException("导入的 Excel 文件没有数据");
        }

        // 只验证，不导入
        int validCount = 0;
        for (int i = 0; i < rowList.size(); i++) {
            DailyConfigExcelDTO row = rowList.get(i);
            int rowNumber = i + 4; // 实际行号（考虑标题行）

            boolean hasValidationError = false;

            if (row.getCities() != null && !row.getCities().isEmpty()) {
                String[] cityNames = row.getCities().split(",");

                for (String cityName : cityNames) {
                    cityName = cityName.trim();
                    if (!cityName.isEmpty()) {
                        // 首先查询城市编码
                        List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);

                        if (regions != null && !regions.isEmpty()) {
                            String cityCode = regions.get(0).getCode();

                            // 检查城市是否在车牌前缀表中存在
                            String platePrefix = sysCityPlatePrefixService.getPlatePrefixByCityCode(cityCode);

                            if (platePrefix == null || platePrefix.isEmpty()) {
                                // 城市在车牌前缀表中不存在
                                failureDetails.add(new BatchImportResultVO.BatchImportFailureDetail(
                                    rowNumber, cityName, "城市在车牌前缀关系表中不存在"
                                ));
                                hasValidationError = true;
                            }
                        } else {
                            // 城市名称未找到匹配的编码
                            failureDetails.add(new BatchImportResultVO.BatchImportFailureDetail(
                                rowNumber, cityName, "城市名称未找到匹配的编码"
                            ));
                            hasValidationError = true;
                        }
                    }
                }
            }

            if (!hasValidationError) {
                validCount++;
            }
        }

        // 返回验证结果
        int failureCount = failureDetails.size();
        if (failureCount == 0) {
            return BatchImportResultVO.success(validCount);
        } else {
            return BatchImportResultVO.failure("验证失败，存在无效的城市数据", failureDetails);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchImportResultVO uploadMultipleFileWithValidation(MultipartFile file) throws Exception {
        // 先验证
        BatchImportResultVO validationResult = validateImportFile(file);

        // 如果验证失败，直接返回验证结果
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 验证通过，开始导入
        List<DailyConfig> dailyConfigList = new ArrayList<>();

        // 重新读取 Excel 内容进行导入
        InputStream inputStream = file.getInputStream();
        ImportParams params = new ImportParams();
        params.setTitleRows(2);  // 跳过前两行（标题行）
        params.setHeadRows(1);   // 第一行作为头行
        params.setNeedSave(true);

        List<DailyConfigExcelDTO> rowList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);

        if (rowList.isEmpty()) {
            throw new IOException("导入的 Excel 文件没有数据");
        }

        // 处理每一行数据（验证已通过，直接处理）
        for (DailyConfigExcelDTO row : rowList) {
            List<String> cityList = new ArrayList<>();

            // 处理城市数据
            if (row.getCities() != null && !row.getCities().isEmpty()) {
                String[] cityNames = row.getCities().split(",");

                for (String cityName : cityNames) {
                    cityName = cityName.trim();
                    if (!cityName.isEmpty()) {
                        // 查询城市编码
                        List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);
                        if (regions != null && !regions.isEmpty()) {
                            String cityCode = regions.get(0).getCode();
                            cityList.add(cityCode);
                            log.info("城市 [{}] 匹配到编码: {}", cityName, cityCode);
                        }
                    }
                }
            }
            DailyConfig config = new DailyConfig();
            config.setClickStart(row.getDailyClickStart());
            config.setClickEnd(row.getDailyClickEnd());
            config.setTenantId(row.getTenantId());

            Map<String, Object> configJson = new HashMap<>();

            // 车险配置
            if (row.getCarInsuranceSwitch() != 0) {
                Map<String, Double> carLedger = new HashMap<>();
                carLedger.put("ledgerStart", row.getCarInsuranceStart());
                carLedger.put("ledgerEnd", row.getCarInsuranceEnd());
                carLedger.put("chatUserStart", row.getChatStart());
                carLedger.put("chatUserEnd", row.getChatEnd());
                configJson.put("carLedger", carLedger);
            }

            // 财险配置
            if (row.getPropertyInsuranceSwitch() != 0) {
                Map<String, Double> financeLedger = new HashMap<>();
                financeLedger.put("ledgerStart", row.getPropertyInsuranceStart());
                financeLedger.put("ledgerEnd", row.getPropertyInsuranceEnd());
                financeLedger.put("chatUserStart", row.getPropertyChatStart());
                financeLedger.put("chatUserEnd", row.getPropertyChatEnd());
                configJson.put("financeLedger", financeLedger);
            }

            // 增值服务配置
            if (row.getValueServiceSwitch() != 0) {
                Map<String, Double> valueAddedLedger = new HashMap<>();
                valueAddedLedger.put("ledgerStart", row.getValueServiceStart());
                valueAddedLedger.put("ledgerEnd", row.getValueServiceEnd());
                valueAddedLedger.put("chatUserStart", row.getValueChatStart());
                valueAddedLedger.put("chatUserEnd", row.getValueChatEnd());
                configJson.put("valueAddedLedger", valueAddedLedger);
            }

            // 城市列表配置
            configJson.put("cityList", cityList);

            // 城市比例配置 - 使用与ClickAutoPreServiceImpl一致的逻辑
            // TODO 以下也需要查看是否有一级城市,需要修改我下列的城市比例代码,并且表格导入的是一一对应的,按照城市逗号隔开和城市占比比例,目前formatCity传参你看下传什么合适
            //DailyConfigContent formattedCityConfig = eaRegionService.formatCity();
            List<DailyConfigContent.CityRatio> cityRatioList = new ArrayList<>();
            if (row.getCityRatios() != null && !row.getCityRatios().isEmpty()) {
                String[] cityRatios = row.getCityRatios().split(",");
                for (int j = 0; j < cityRatios.length && j < cityList.size(); j++) {
                    String cityRatio = cityRatios[j].trim();
                    if (!cityRatio.isEmpty()) {
                        try {
                            String cityCode = cityList.get(j);
                            Double ratio = Double.parseDouble(cityRatio);

                            // 创建城市比例对象
                            DailyConfigContent.CityRatio cityRatioObj = new DailyConfigContent.CityRatio();
                            cityRatioObj.setCityCode(cityCode);
                            cityRatioObj.setRatio(ratio);
                            cityRatioList.add(cityRatioObj);
                            log.info("Excel导入 - 城市编码: {} 比例: {}%", cityCode, ratio);
                        } catch (NumberFormatException e) {
                            log.warn("Excel导入 - 城市比例格式错误: {}", cityRatio);
                        }
                    }
                }
            }

            // 使用formatCity方法处理一级城市编码，将其转换为二级城市编码并分配比例
            DailyConfigContent formattedCityConfig = eaRegionService.formatCity(cityRatioList);
            JSONArray objects = new JSONArray();
            if (formattedCityConfig != null && formattedCityConfig.getCityRatios() != null) {
                for (DailyConfigContent.CityRatio cityRatio : formattedCityConfig.getCityRatios()) {
                    Map<String, Object> cityRatioMap = new HashMap<>();
                    cityRatioMap.put("cityCode", cityRatio.getCityCode());
                    cityRatioMap.put("ratio", cityRatio.getRatio());
                    objects.add(cityRatioMap);
                }
                log.info("Excel导入 - 城市比例格式化完成，一级城市已转换为二级城市，共 {} 个城市比例配置", formattedCityConfig.getCityRatios().size());
            }
            configJson.put("cityRatios", objects);

            config.setConfigJson(JSON.toJSONString(configJson));
            dailyConfigList.add(config);
        }

        // 保存数据
        int successCount = 0;
        if (!dailyConfigList.isEmpty()) {
            // 删除现有配置
            List<Integer> tenantIdList = dailyConfigList.stream()
                .map(DailyConfig::getTenantId)
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(tenantIdList)) {
                this.lambdaUpdate()
                    .in(DailyConfig::getTenantId, tenantIdList)
                    .remove();
            }

            // 保存新配置
            saveBatch(dailyConfigList);
            successCount = dailyConfigList.size();
        }

        // 返回成功结果
        return BatchImportResultVO.success(successCount);
    }


}
