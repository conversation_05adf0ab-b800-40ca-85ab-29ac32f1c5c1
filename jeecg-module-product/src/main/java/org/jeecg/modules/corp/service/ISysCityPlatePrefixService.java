package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.dto.CityConfigDTO;
import org.jeecg.modules.corp.dto.CityConfigSearchDTO;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;

import java.util.List;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
public interface ISysCityPlatePrefixService extends IService<SysCityPlatePrefix> {

    /**
     * 根据城市编码查询车牌前缀
     * @param cityCode 城市编码
     * @return 车牌前缀
     */
    String getPlatePrefixByCityCode(String cityCode);

    /**
     * 分页查询城市配置
     * @param searchDTO 搜索条件
     * @return 分页结果
     */
    IPage<SysCityPlatePrefix> searchCityConfigs(CityConfigSearchDTO searchDTO);

    /**
     * 添加城市配置
     * @param cityConfigDTO 城市配置信息
     * @return 是否成功
     */
    boolean addCityConfig(CityConfigDTO cityConfigDTO);

    /**
     * 更新城市配置
     * @param cityConfigDTO 城市配置信息
     * @return 是否成功
     */
    boolean updateCityConfig(CityConfigDTO cityConfigDTO);

    /**
     * 删除城市配置
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteCityConfig(String id);

    /**
     * 根据ID查询城市配置
     * @param id 主键ID
     * @return 城市配置信息
     */
    CityConfigDTO getCityConfigById(String id);

    /**
     * 批量同步城市数据
     * @return 是否成功
     */
    boolean syncCityData();
}
