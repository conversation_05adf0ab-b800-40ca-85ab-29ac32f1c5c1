package org.jeecg.modules.corp.util;

import java.util.Random;

public class RandomCarBrandGenerator {
    private static final Random random = new Random();

    private static final String[] CAR_BRANDS = {
            // 大众品牌（传统燃油车，80万以下）
            "丰田", "本田", "日产", "大众", "现代", "起亚", "福特", "雪佛兰",
            "别克", "雪铁龙", "标致", "马自达", "三菱", "斯柯达", "铃木", "Jeep",
            "吉利", "长安", "长城", "奇瑞", "比亚迪", "东风", "上汽", "广汽",
            "一汽", "北汽", "江淮", "海马", "众泰", "力帆", "东南", "华晨",
            "金杯", "启辰", "传祺", "荣威", "名爵", "五菱", "宝骏", "猎豹",
            "观致", "陆风", "捷途", "凯翼", "奔腾", "领克", "星途", "宝沃",
            "华颂", "野马", "江铃",

            // BBA及豪华品牌
            "奔驰", "宝马", "奥迪", "雷克萨斯", "凯迪拉克", "林肯", "沃尔沃",
            "捷豹", "路虎", "英菲尼迪",
            "红旗", "哈弗", "WEY", "坦克", "领克", "星途", "腾势", "极星",

            // 新能源品牌（新势力/传统车企新能源子品牌）
            "特斯拉", "蔚来", "小鹏", "理想", "哪吒", "威马", "零跑", "极氪",
            "岚图", "埃安", "欧拉", "几何", "极狐", "智己", "赛力斯", "合众",
            "高合", "广汽", "长安", "吉利", "比亚迪",
            "上汽大通", "东风小康", "开瑞",
            "小虎", "思皓", "轻橙", "奇瑞", "荣威", "名爵", "鸿蒙智行", "智界", "小米汽车", "AITO问界", "深蓝汽车",
            "阿维塔", "飞凡", "仰望", "方程豹", "宾理", "创维汽车",
            "睿蓝", "雷丁", "御捷"
    };

    /**
     * 随机返回一个汽车品牌名称（中文）
     * @return 中文品牌名称
     */
    public static String generateRandomCarBrand() {
        return CAR_BRANDS[random.nextInt(CAR_BRANDS.length)];
    }

    /**
     * 检查传入的品牌名称中是否包含预定义品牌数组中的任何一个品牌
     * @param inputBrand 输入的品牌名称（如：梅赛德斯-奔驰牌BJ7154K）
     * @return 如果包含则返回匹配的品牌名称，否则返回null
     */
    public static String findContainedBrand(String inputBrand) {
        if (inputBrand == null || inputBrand.trim().isEmpty()) {
            return null;
        }

        String trimmedInput = inputBrand.trim();
        for (String brand : CAR_BRANDS) {
            if (trimmedInput.contains(brand)) {
                return brand;
            }
        }
        return null;
    }

    /**
     * 获取标准化的品牌名称
     * 如果传参中包含CAR_BRANDS中的品牌，则返回CAR_BRANDS中的品牌值
     * 否则返回传参的原值
     * @param inputBrand 输入的品牌名称
     * @return 标准化的品牌名称
     */
    public static String getStandardizedBrand(String inputBrand) {
        String containedBrand = findContainedBrand(inputBrand);
        if (containedBrand != null) {
            return containedBrand;
        } else {
            return inputBrand; // 返回原值，包括null和空字符串
        }
    }

    // 测试方法
    public static void main(String[] args) {
        // 测试包含关系的情况
        System.out.println("=== 测试包含关系逻辑 ===");
        String[] testCases = {
            "梅赛德斯-奔驰牌BJ7154K",  // 应该返回"奔驰"
            "宝马X5豪华版",           // 应该返回"宝马"
            "丰田凯美瑞2.5L",        // 应该返回"丰田"
            "未知品牌ABC123",        // 应该返回原值"未知品牌ABC123"
            null,                   // 应该返回null
            "",                     // 应该返回""
            "   "                   // 应该返回"   "
        };

        for (String testCase : testCases) {
            String result = getStandardizedBrand(testCase);
            System.out.println("输入: \"" + testCase + "\" -> 输出: \"" + result + "\"");
        }

        System.out.println("\n总品牌数: " + CAR_BRANDS.length);
    }
}
