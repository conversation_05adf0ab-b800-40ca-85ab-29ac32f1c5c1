package org.jeecg.modules.corp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 城市配置搜索DTO
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Data
@ApiModel(value="城市配置搜索DTO", description="城市配置搜索条件")
public class CityConfigSearchDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 城市编码 */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /** 完整城市名称 */
    @ApiModelProperty(value = "完整城市名称")
    private String allCityName;

    /** 顶级城市名称 */
    @ApiModelProperty(value = "顶级城市名称")
    private String topCityName;

    /** 车牌前缀 */
    @ApiModelProperty(value = "车牌前缀")
    private String platePrefix;

    /** 页码 */
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNo = 1;

    /** 页大小 */
    @ApiModelProperty(value = "页大小", example = "10")
    private Integer pageSize = 10;
}
