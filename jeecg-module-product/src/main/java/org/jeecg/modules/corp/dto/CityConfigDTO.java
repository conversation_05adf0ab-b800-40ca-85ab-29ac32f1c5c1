package org.jeecg.modules.corp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 城市配置DTO
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Data
@ApiModel(value="城市配置DTO", description="城市配置数据传输对象")
public class CityConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;

    /** 城市编码 */
    @NotBlank(message = "城市编码不能为空")
    @ApiModelProperty(value = "城市编码", required = true)
    private String cityCode;

    /** 地区表城市名称 */
    @NotBlank(message = "地区表城市名称不能为空")
    @ApiModelProperty(value = "地区表城市名称", required = true)
    private String regionCityName;

    /** 完整城市名称 */
    @NotBlank(message = "完整城市名称不能为空")
    @ApiModelProperty(value = "完整城市名称", required = true)
    private String allCityName;

    /** 顶级城市名称 */
    @NotBlank(message = "顶级城市名称不能为空")
    @ApiModelProperty(value = "顶级城市名称", required = true)
    private String topCityName;

    /** 车牌前缀 */
    @NotBlank(message = "车牌前缀不能为空")
    @ApiModelProperty(value = "车牌前缀", required = true)
    private String platePrefix;

    /** 上级城市编码 */
    @ApiModelProperty(value = "上级城市编码")
    private String parentCityCode;

    /** 上级城市名称 */
    @ApiModelProperty(value = "上级城市名称")
    private String parentCityName;
}
