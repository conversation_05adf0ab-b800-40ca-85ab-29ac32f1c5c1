package org.jeecg.modules.corp.dto;
import java.util.List;
import java.util.Random;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@Data
public class DailyConfigContent {

    @ApiModelProperty("财险台账区间")
    private Range financeLedger;

    @ApiModelProperty("增值服务台账区间")
    private Range valueAddedLedger;

    @ApiModelProperty("车险台账区间")
    private Range carLedger;

    @ApiModelProperty("城市列表")
    private List<String> cityList;  // 修改为 List<String>，存储城市编码

    @ApiModelProperty("城市配比列表")
    private List<CityRatio> cityRatios;  // 城市配比数据

    @ApiModelProperty("充值记录列表")
    private List<RechargeRecord> rechargeRecords;  // 充值记录数组

    @ApiModelProperty("消耗单价开始")
    private Double priceStart;  // 消耗单价区间开始

    @ApiModelProperty("消耗单价结束")
    private Double priceEnd;  // 消耗单价区间结束

    /**
     * 获取随机城市（支持按配比随机选择）
     */
    public String getRandomCity() {
        // 如果有城市配比数据，按配比随机选择
        if (cityRatios != null && !cityRatios.isEmpty()) {
            return getRandomCityByRatio();
        }

        // 如果没有配比数据，使用原有的均匀随机选择
        if (cityList == null || cityList.isEmpty()) return null;
        return cityList.get(new Random().nextInt(cityList.size()));
    }

    /**
     * 按配比随机选择城市
     */
    private String getRandomCityByRatio() {
        if (cityRatios == null || cityRatios.isEmpty()) {
            return null;
        }

        // 计算总配比（应该是100，但为了安全起见重新计算）
        double totalRatio = cityRatios.stream()
                .mapToDouble(CityRatio::getRatio)
                .sum();

        if (totalRatio <= 0) {
            return null;
        }

        // 生成0到totalRatio之间的随机数
        Random random = new Random();
        double randomValue = random.nextDouble() * totalRatio;

        // 根据配比选择城市
        double currentSum = 0;
        for (CityRatio cityRatio : cityRatios) {
            currentSum += cityRatio.getRatio();
            if (randomValue <= currentSum) {
                return cityRatio.getCityCode();
            }
        }

        // 如果由于浮点数精度问题没有选中任何城市，返回最后一个城市
        return cityRatios.get(cityRatios.size() - 1).getCityCode();
    }

    @Data
    public static class Range {
        @ApiModelProperty("台账区间开始")
        private Double ledgerStart;

        @ApiModelProperty("台账区间结束")
        private Double ledgerEnd;

        @ApiModelProperty("聊天用户区间开始")
        private Double chatUserStart;

        @ApiModelProperty("聊天用户区间结束")
        private Double chatUserEnd;
    }

    @Data
    public static class CityRatio {
        @ApiModelProperty("城市编码")
        private String cityCode;

        @ApiModelProperty("配比百分比")
        private Double ratio;
    }

    @Data
    public static class RechargeRecord {
        @ApiModelProperty("充值日期")
        private String rechargeDate;

        @ApiModelProperty("消耗金额")
        private Double consumeAmount;
    }
}
