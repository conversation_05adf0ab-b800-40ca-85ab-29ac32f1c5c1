package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.dto.DeleteByTypeDTO;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.entity.PdCarInfoRel;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.corp.mapper.PdLedgerMapper;
import org.jeecg.modules.corp.service.*;
import org.jeecg.modules.corp.entity.PdInsuranceLedger;
import org.jeecg.modules.corp.entity.PdAddedLedger;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.entity.PdGuestUsersRel;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 台账
 * @Author: jeecg-boot
 * @Date:   2024-11-08
 * @Version: V1.0
 */
@Service
public class PdLedgerServiceImpl extends ServiceImpl<PdLedgerMapper, PdLedger> implements IPdLedgerService {

    @Resource
    private IPdCarInfoService carInfoService;

    @Resource
    private IPdCarInfoRelService carInfoRelService;

    @Resource
    private IPdGuestUsersRelService guestUsersRelService;

    @Resource
    private IPdChatService pdChatService;

    @Resource
    private IPdChatUserService pdChatUserService;
    @Resource
    private IClickReportHourlyService clickReportHourlyService;

    @Resource
    private IClickReportService clickReportService;
    @Resource
    private IPdCasualtyInfoService casualtyInfoService;
    @Resource
    private IPdAddedService addedService;
    @Resource
    private IPdInsuranceLedgerService insuranceLedgerService;
    @Resource
    private IPdAddedLedgerService addedLedgerService;

    @Resource
    private IPdGuestUsersService guestUsersService;

    @Override
    public IPage<PdLedger> pageList(Page<PdLedger> page, LedgerListDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }

        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }
        return this.baseMapper.pageList(page,dto);
    }



    @Override
    @Transactional
    public void deleteByTypeAndDateRange(DeleteByTypeDTO dto) {
        // 检查租户ID列表是否为空
        if (dto.getTenantIds() == null || dto.getTenantIds().isEmpty()) {
            return;
        }


        // 格式化日期为字符串
        String startDateStr = dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endDateStr = dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        //删除时段表与日点击表
        clickReportHourlyService.remove(new LambdaQueryWrapper<ClickReportHourly>()
                .in(ClickReportHourly::getTenantId, dto.getTenantIds())
                .between(ClickReportHourly::getStatDate, startDateStr, endDateStr)
        );

        clickReportService.remove(new LambdaQueryWrapper<ClickReport>()
                .in(ClickReport::getTenantId, dto.getTenantIds())
                .between(ClickReport::getStatDate, startDateStr, endDateStr)
        );

        // 1. 批量查询符合条件的 PdLedger 记录
        LambdaQueryWrapper<PdLedger> ledgerQuery = new LambdaQueryWrapper<>();
        ledgerQuery.between(PdLedger::getSignDate, startDateStr, endDateStr)
                .in(PdLedger::getTenantId, dto.getTenantIds());

        List<PdLedger> ledgerList = this.list(ledgerQuery);
        if (!ledgerList.isEmpty()) {
            // 提取 ledgerIds
            List<String> ledgerIds = ledgerList.stream()
                    .map(PdLedger::getId)
                    .collect(Collectors.toList());
            this.removeByIds(ledgerIds);
        }
        // 2. 批量查询关联的 PdCarInfo
        String startTime = startDateStr + " 00:00:00";
        String endTime = endDateStr + " 23:59:59";

        // 删除财险台账及相关数据
        deleteInsuranceLedgerAndRelatedData(dto.getTenantIds(), startDateStr, endDateStr, startTime, endTime);

        // 删除增值服务台账及相关数据
        deleteAddedLedgerAndRelatedData(dto.getTenantIds(), startDateStr, endDateStr, startTime, endTime);

        List<PdCarInfo> carInfoList = carInfoService.lambdaQuery()
                .between(PdCarInfo::getCreateTime, startTime, endTime)
                .in(PdCarInfo::getTenantId, dto.getTenantIds())
                .list();
        if (carInfoList.isEmpty()) {
            return;

        }

        // 提取 carInfoIds 和 guestIds
        List<String> carInfoIds = carInfoList.stream()
                .map(PdCarInfo::getId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        List<String> guestIds = guestUsersRelService.lambdaQuery()
                .select(PdGuestUsersRel::getGuestId)
                .in(PdGuestUsersRel::getPid, carInfoIds)
                .list()
                .stream()
                .map(PdGuestUsersRel::getGuestId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 3. 批量删除相关数据
        // 3.1 删除聊天记录 (PdChat, 根据 userId = guestId)
        if (!guestIds.isEmpty()) {
            pdChatService.remove(new LambdaQueryWrapper<PdChat>()
                    .in(PdChat::getUserId, guestIds));
        }

        // 3.2 删除 PdCarInfoRel (根据 carInfoId)
        if (!carInfoIds.isEmpty()) {
            carInfoRelService.remove(new LambdaQueryWrapper<PdCarInfoRel>()
                    .in(PdCarInfoRel::getCarInfoId, carInfoIds));
        }

        // 3.3 删除聊天用户 (PdChatUser, 根据 keyId = ledgerId)
        if (!guestIds.isEmpty()) {
            pdChatUserService.remove(new LambdaQueryWrapper<PdChatUser>()
                    .in(PdChatUser::getUserId, guestIds));
        }

        // 3.4 删除 PdCarInfo (根据 carInfoId)
        if (!carInfoIds.isEmpty()) {
            carInfoService.removeByIds(carInfoIds);
        }

        // 3.5 删除 PdGuestUsers (根据 guestId)
        if (!guestIds.isEmpty()) {
            guestUsersService.removeByIds(guestIds);
            LambdaQueryWrapper<PdGuestUsersRel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(PdGuestUsersRel::getGuestId,guestIds);
            guestUsersRelService.remove(queryWrapper);
        }
    }

    /**
     * 删除财险台账及相关数据
     */
    private void deleteInsuranceLedgerAndRelatedData(List<String> tenantIds, String startDateStr, String endDateStr, String startTime, String endTime) {
        // 1. 查询符合条件的财险台账
        List<PdInsuranceLedger> insuranceLedgerList = insuranceLedgerService.lambdaQuery()
                .between(PdInsuranceLedger::getOrderDate, startDateStr, endDateStr)
                .in(PdInsuranceLedger::getTenantId, tenantIds)
                .list();


        // 提取财险台账ID列表
        List<String> insuranceLedgerIds = insuranceLedgerList.stream()
                .map(PdInsuranceLedger::getId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 2. 查询关联的财险预约信息
        List<PdCasualtyInfo> casualtyInfoList = casualtyInfoService.lambdaQuery()
                .between(PdCasualtyInfo::getCreateTime, startTime, endTime)
                .in(PdCasualtyInfo::getTenantId, tenantIds)
                .list();

        if (!casualtyInfoList.isEmpty()) {
            // 提取财险预约信息ID列表
            List<String> casualtyInfoIds = casualtyInfoList.stream()
                    .map(PdCasualtyInfo::getId)
                    .filter(id -> id != null)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询游客关系信息
            List<String> guestIds = guestUsersRelService.lambdaQuery()
                    .select(PdGuestUsersRel::getGuestId)
                    .in(PdGuestUsersRel::getPid, casualtyInfoIds)
                    .list()
                    .stream()
                    .map(PdGuestUsersRel::getGuestId)
                    .filter(id -> id != null)
                    .distinct()
                    .collect(Collectors.toList());

            // 删除相关数据
            deleteRelatedDataByGuestIds(guestIds, casualtyInfoIds);
            casualtyInfoService.removeByIds(casualtyInfoIds);
        }

        // 3. 删除财险台账（会自动删除关联的财险预约信息）
        if (!insuranceLedgerIds.isEmpty()) {
            insuranceLedgerService.deleteBatch(insuranceLedgerIds);
        }
    }

    /**
     * 删除增值服务台账及相关数据
     */
    private void deleteAddedLedgerAndRelatedData(List<String> tenantIds, String startDateStr, String endDateStr, String startTime, String endTime) {
        // 1. 查询符合条件的增值服务台账
        List<PdAddedLedger> addedLedgerList = addedLedgerService.lambdaQuery()
                .between(PdAddedLedger::getOrderDate, startDateStr, endDateStr)
                .in(PdAddedLedger::getTenantId, tenantIds)
                .list();


        // 提取增值服务台账ID列表
        List<String> addedLedgerIds = addedLedgerList.stream()
                .map(PdAddedLedger::getId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 2. 查询关联的增值服务预约记录
        List<PdAdded> addedList = addedService.lambdaQuery()
                .between(PdAdded::getCreateTime, startTime, endTime)
                .in(PdAdded::getTenantId, tenantIds)
                .list();

        if (!addedList.isEmpty()) {
            // 提取增值服务预约记录ID列表
            List<String> addedIds = addedList.stream()
                    .map(PdAdded::getId)
                    .filter(id -> id != null)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询游客关系信息
            List<String> guestIds = guestUsersRelService.lambdaQuery()
                    .select(PdGuestUsersRel::getGuestId)
                    .in(PdGuestUsersRel::getPid, addedIds)
                    .list()
                    .stream()
                    .map(PdGuestUsersRel::getGuestId)
                    .filter(id -> id != null)
                    .distinct()
                    .collect(Collectors.toList());

            // 删除相关数据
            deleteRelatedDataByGuestIds(guestIds, addedIds);
            addedService.removeByIds(addedIds);
        }

        // 3. 删除增值服务台账（会自动删除关联的增值服务预约记录）
        if (!addedLedgerIds.isEmpty()) {
            addedLedgerService.deleteBatch(addedLedgerIds);
        }
    }

    /**
     * 根据游客ID列表删除相关数据
     */
    private void deleteRelatedDataByGuestIds(List<String> guestIds, List<String> pidList) {
        if (guestIds.isEmpty()) {
            return;
        }

        // 删除聊天记录
        pdChatService.remove(new LambdaQueryWrapper<PdChat>()
                .in(PdChat::getUserId, guestIds));

        // 删除聊天用户
        pdChatUserService.remove(new LambdaQueryWrapper<PdChatUser>()
                .in(PdChatUser::getUserId, guestIds));

        // 删除游客用户
        guestUsersService.removeByIds(guestIds);

        // 删除游客关系
        guestUsersRelService.remove(new LambdaQueryWrapper<PdGuestUsersRel>()
                .in(PdGuestUsersRel::getGuestId, guestIds));
        if (pidList != null && !pidList.isEmpty()) {
            guestUsersRelService.remove(new LambdaQueryWrapper<PdGuestUsersRel>()
                    .in(PdGuestUsersRel::getPid, pidList));
        }


    }
}
