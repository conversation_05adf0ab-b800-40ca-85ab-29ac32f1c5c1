package org.jeecg.modules.corp.controller;

import java.io.IOException;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.corp.service.IPdChatUserService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.corp.service.IPdLinkInfoService;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.dto.ExcelChatDto;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.jeecg.modules.wechat.vo.chat.ChatFormVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 聊天用户
 * @Author: jeecg-boot
 * @Date:   2024-11-09
 * @Version: V1.0
 */
@Api(tags="聊天用户")
@RestController
@RequestMapping("/corp/pdChatUser")
@Slf4j
public class PdChatUserController extends JeecgController<PdChatUser, IPdChatUserService> {
	@Autowired
	private IPdChatUserService pdChatUserService;
	@Autowired
	private IPdChatService pdChatService;
 	@Resource
 	private IPdGuestUsersService pdGuestUsersService;

    @Resource
	private IPdCarInfoService pdCarInfoService;
	@Resource
	private IPdLinkInfoService pdLinkInfoService;
	@Resource
	private IPdAddedService pdAddedService;


	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	//@AutoLog(value = "聊天用户-分页列表查询")
	@ApiOperation(value="聊天用户-分页列表查询", notes="聊天用户-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdChatUser>> queryPageList(
			PdChatUser pdChatUser,
			@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
			@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
			@RequestParam(name="dateRange", required=false) String dateRange,
			HttpServletRequest req) {

		// 执行分页查询，将查询逻辑移到Service层
		Page<PdChatUser> page = new Page<>(pageNo, pageSize);
		IPage<PdChatUser> pageList = pdChatUserService.queryPageList(page, pdChatUser, dateRange);

		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param pdChatUser
	 * @return
	 */
	@AutoLog(value = "聊天用户-添加")
	@ApiOperation(value="聊天用户-添加", notes="聊天用户-添加")
	@RequiresPermissions("corp:pd_chat_user:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdChatUser pdChatUser) {
		pdChatUserService.save(pdChatUser);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdChatUser
	 * @return
	 */
	@AutoLog(value = "聊天用户-编辑")
	@ApiOperation(value="聊天用户-编辑", notes="聊天用户-编辑")
	@RequiresPermissions("corp:pd_chat_user:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdChatUser pdChatUser) {
		pdChatUserService.updateById(pdChatUser);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聊天用户-通过id删除")
	@ApiOperation(value="聊天用户-通过id删除", notes="聊天用户-通过id删除")
	@RequiresPermissions("corp:pd_chat_user:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdChatUserService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "聊天用户-批量删除")
	@ApiOperation(value="聊天用户-批量删除", notes="聊天用户-批量删除")
	@RequiresPermissions("corp:pd_chat_user:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdChatUserService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "聊天用户-通过id查询")
	@ApiOperation(value="聊天用户-通过id查询", notes="聊天用户-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdChatUser> queryById(@RequestParam(name="id",required=true) String id) {
		PdChatUser pdChatUser = pdChatUserService.getById(id);
		if(pdChatUser==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdChatUser);
	}

	 @RequestMapping(value = "/exportXls")
	 public ModelAndView exportXls(HttpServletRequest request, PdChatUser pdChatUser, ExcelChatDto dto) {
		 // Step 1: 初始化导出列表
		 List<ChatFormVo> exportList = new ArrayList<>();

		 try {
			 // Step 2: 检查是否有选中的数据
			 String selections = request.getParameter("selections");
			 List<PdChatUser> userList;

			 if (oConvertUtils.isNotEmpty(selections)) {
				 // 如果有选中数据，按选中的ID查询
				 List<String> selectionList = Arrays.asList(selections.split(","));
				 userList = pdChatUserService.lambdaQuery()
						 .in(PdChatUser::getId, selectionList) // 按选中ID筛选
						 .list();
			 } else {
				 // 如果没有选中数据，则按前端传参的查询条件筛选
				 userList = pdChatUserService.lambdaQuery()
						 .eq(dto.getTenantId() != null, PdChatUser::getTenantId, dto.getTenantId()) // 按租户筛选
						 .eq(dto.getWideType() != null, PdChatUser::getWideType, dto.getWideType()) // 按广告类型筛选
						 .ge(dto.getStartDate() != null, PdChatUser::getDiverData, dto.getStartDate()) // 引流日期 >= 开始日期
						 .le(dto.getEndDate() != null, PdChatUser::getDiverData, dto.getEndDate()) // 引流日期 <= 结束日期
						 .like(pdChatUser.getUserName() != null, PdChatUser::getUserName, pdChatUser.getUserName()) // 用户名称模糊搜索
						 .list();
			 }

			 // Step 3: 整合用户和聊天记录
			 for (PdChatUser user : userList) {
				 // 查询该用户的聊天记录
				 List<PdChat> chatList = pdChatService.lambdaQuery()
						 .eq(PdChat::getUserId, user.getUserId())
						 .orderByAsc(PdChat::getSendTime) // 按发送时间升序排序
						 .list();

				 // 遍历聊天记录，生成导出的 VO
				 int sequence = 1; // 消息序号，从 1 开始
				 for (PdChat chat : chatList) {
					 ChatFormVo vo = new ChatFormVo();
					 vo.setSequence(sequence++); // 消息序号
					 vo.setUserName(user.getUserName()); // 用户名称
					 vo.setWideType(user.getWideType() == null ? "空"
							 : user.getWideType() == 0 ? "车险"
							 : user.getWideType() == 1 ? "财险"
							 : "增值服务");					 vo.setSender(chat.getSendType() == 0 ? "用户" : "客服"); // 消息发送方
					 vo.setMessageContent(chat.getMessage()); // 消息内容
					 vo.setSendTime(chat.getSendTime()); // 消息发送时间
					 vo.setDiverData(user.getDiverData()); // 引流日期
					 vo.setChatUserId(user.getId()); // 聊天用户主键 ID
					 vo.setChatId(chat.getId()); // 聊天主键 ID
					 vo.setTenantId(user.getTenantId()); // 租户 ID

					 exportList.add(vo); // 添加到导出列表
				 }
			 }
		 } catch (Exception e) {
			 log.error("导出失败：", e);

			 Map<String, Object> map = new HashMap<>();
			 map.put("params", new ExportParams("导出失败", "错误信息"));

			 return new ModelAndView(new JeecgEntityExcelView(), map);
		 }

		 // Step 4: 设置导出参数
		 ExportParams exportParams = new ExportParams("聊天记录报表", "导出人：" + ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getRealname(), "聊天记录");


		 // Step 5: 返回导出视图
		 ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		 mv.addObject(NormalExcelConstants.FILE_NAME, "聊天记录");
		 mv.addObject(NormalExcelConstants.CLASS, ChatFormVo.class);
		 mv.addObject(NormalExcelConstants.PARAMS, exportParams);
		 mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

		 return mv;
	 }


	 @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	 public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		 MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		 Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

		 for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			 // 获取上传文件对象
			 MultipartFile file = entity.getValue();
			 ImportParams params = new ImportParams();
			 params.setTitleRows(2);
			 params.setHeadRows(1);
			 params.setNeedSave(true);

			 try {
				 // 使用 ChatFormVo 解析 Excel 数据
				 List<ChatFormVo> chatFormList = ExcelImportUtil.importExcel(file.getInputStream(), ChatFormVo.class, params);

				 // 遍历每条聊天记录
				 for (ChatFormVo chatForm : chatFormList) {
					 int winType = "用户".equals(chatForm.getSender()) ? 0 : 1;
					 PdGuestUsers guestUser = new PdGuestUsers();
					 String wideType = chatForm.getWideType();
					 Integer wideInt = 0;
					  if (wideType.equals("财险")) {
						 wideInt = 1;
					 } else if (wideType.equals("增值服务")) {
						 wideInt = 2;
					 }

					 // 处理聊天用户
					 if (StrUtil.isEmpty(chatForm.getChatUserId())) {
						 //聊天用户不存在,根据类型,已经时间,查询日期相同的台账信息,根据其聊天用户来设置
						 if (wideInt == 0) {
							 //查询车险用户
							 //pdCarInfoService.getById()

						 }else if (wideInt == 1) {

						 }else if (wideInt == 2) {

						 }
						 PdChatUser pdChatUser = new PdChatUser();
						 pdChatUser.setUserName(chatForm.getUserName());
						 pdChatUser.setWideType(wideInt);
						 // 如果聊天用户 ID 为空，创建新用户
						 guestUser = pdGuestUsersService.createGuestUser(null);
						 pdChatUser.setUserId(guestUser.getId());
						 pdChatUserService.saveOrUpdate(pdChatUser);
						 chatForm.setChatUserId(guestUser.getId()); // 设置新生成的用户 ID
					 }else {
						 PdChatUser chatUser = this.pdChatUserService.getById(chatForm.getChatUserId());
						 guestUser.setId(chatUser.getUserId());
					 }

					 // 处理聊天记录
					 PdChat chatRecord = new PdChat();
					 chatRecord.setId(chatForm.getChatId());
					 chatRecord.setUserId(guestUser.getId());
					 chatRecord.setMessage(chatForm.getMessageContent());
					 chatRecord.setSendTime(chatForm.getSendTime());
					 chatRecord.setSendType("用户".equals(chatForm.getSender()) ? 0 : 1);
					 chatRecord.setTenantId(chatForm.getTenantId());

					 // 保存或更新聊天记录
					 pdChatService.saveOrUpdate(chatRecord);
				 }

				 return Result.ok("文件导入成功！");
			 } catch (Exception e) {
				 // 捕获异常并返回错误信息
				 String msg = e.getMessage();
				 log.error(msg, e);
				 if (msg != null && msg.contains("Duplicate entry")) {
					 return Result.error("文件导入失败:有重复数据！");
				 } else {
					 return Result.error("文件导入失败:" + e.getMessage());
				 }
			 } finally {
				 try {
					 file.getInputStream().close();
				 } catch (IOException e) {
					 e.printStackTrace();
				 }
			 }
		 }
		 return Result.error("文件导入失败！");
	 }

}
