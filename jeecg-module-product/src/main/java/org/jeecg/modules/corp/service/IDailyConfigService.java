package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.jeecg.modules.corp.vo.BatchImportResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
* 每日租户配置表服务类
*
* <AUTHOR>
* @since 2025-04-12
*/
public interface IDailyConfigService extends IService<DailyConfig> {
    /**
    * 新增每日租户配置表
    */
    DailyConfig add(DailyConfig dto);

    /**
    * 修改每日租户配置表
    */
    DailyConfig edit(DailyConfig dto);

    /**
    * 根据id获取每日租户配置表 详情
    */
    DailyConfig queryById(Integer tenantId);

    void uploadMultipleFile(MultipartFile file) throws Exception;

    /**
     * 批量导入配置文件（带城市验证）
     * @param file Excel文件
     * @return 导入结果
     * @throws Exception 异常
     */
    BatchImportResultVO uploadMultipleFileWithValidation(MultipartFile file) throws Exception;

    /**
     * 验证批量导入文件（只验证不导入）
     * @param file Excel文件
     * @return 验证结果
     * @throws Exception 异常
     */
    BatchImportResultVO validateImportFile(MultipartFile file) throws Exception;
}


