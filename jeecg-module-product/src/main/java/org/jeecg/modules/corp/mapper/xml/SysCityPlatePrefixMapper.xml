<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.SysCityPlatePrefixMapper">

    <!-- 根据城市编码查询车牌前缀 -->
    <select id="getPlatePrefixByCityCode" resultType="java.lang.String">
        SELECT
            plate_prefix
        FROM
            sys_city_plate_prefix
        WHERE
            city_code = #{cityCode}
        LIMIT 1
    </select>

    <!-- 根据城市编码查询城市配置信息 -->
    <select id="getCityConfigByCode" resultType="org.jeecg.modules.corp.entity.SysCityPlatePrefix">
        SELECT
            id,
            city_code as cityCode,
            city_name as cityName,
            all_city_name as allCityName,
            top_city_name as topCityName,
            plate_prefix as platePrefix,
            parent_city_code as parentCityCode
        FROM
            sys_city_plate_prefix
        WHERE
            city_code = #{cityCode}
        LIMIT 1
    </select>

</mapper>
