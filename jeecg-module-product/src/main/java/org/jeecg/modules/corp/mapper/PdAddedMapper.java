package org.jeecg.modules.corp.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 增值服务预约记录
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
public interface PdAddedMapper extends BaseMapper<PdAdded> {

    IPage<PdAdded> pageList(Page<PdAdded> page, @Param("dto") LedgerListDto dto);

    PdAdded queryDetailById(@Param("id") String id);
}
