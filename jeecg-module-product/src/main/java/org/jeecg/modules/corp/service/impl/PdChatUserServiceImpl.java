package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TimeRestrictionUtil;
import org.jeecg.config.mybatis.TenantUtils;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.corp.mapper.PdChatUserMapper;
import org.jeecg.modules.corp.entity.PdChatUser;
import org.jeecg.modules.corp.service.IPdChatUserService;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdChatSource;
import org.jeecg.modules.info.entity.PdChatSourceDet;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdChatSourceDetService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 聊天用户
 * @Author: jeecg-boot
 * @Date:   2024-11-09
 * @Version: V1.0
 */
@Service
public class PdChatUserServiceImpl extends ServiceImpl<PdChatUserMapper, PdChatUser> implements IPdChatUserService {

    @Autowired
    private TimeRestrictionUtil timeRestrictionUtil;

    @Override
    public IPage<PdChatUser> queryPageList(Page<PdChatUser> page, PdChatUser pdChatUser, String dateRange) {
        // 创建一个新的 QueryWrapper，不使用 QueryGenerator.initQueryWrapper 以避免可能的默认排序
        QueryWrapper<PdChatUser> queryWrapper = new QueryWrapper<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户没有权限查看所有租户数据，则按照原来的逻辑限制租户ID
        if (!hasAllDataPermission) {
            // 添加租户ID过滤条件
            queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
        }
        // 如果用户有权限查看所有租户数据，则不添加租户ID过滤条件

        String startDateStr = null;
        String endDateStr = null;

        // 解析 dateRange 参数 (格式: "2025-03-12,2025-03-17")
        if (StringUtils.isNotBlank(dateRange)) {
            String[] dates = dateRange.split(",");
            if (dates.length == 2) {
                startDateStr = dates[0].trim();
                endDateStr = dates[1].trim();
            }
        }

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantUtils.getCurrentTenantId());

        // 处理时间范围，确保不超过当前时间
        startDateStr = timeRestrictionUtil.processStartDate(startDateStr, canViewFutureData);
        endDateStr = timeRestrictionUtil.processEndDate(endDateStr, canViewFutureData);

        if (StringUtils.isNotBlank(startDateStr)) {
            String startDateTime = startDateStr + " 00:00:00";
            queryWrapper.ge("diver_data", startDateTime); // 起始时间
        }
        if (StringUtils.isNotBlank(endDateStr)) {
            String endDateTime = endDateStr + " 23:59:59";
            queryWrapper.le("diver_data", endDateTime);   // 结束时间
        }

        // 添加实体中的其他查询条件
        if (pdChatUser != null) {
            if (pdChatUser.getTenantId() != null) {
                queryWrapper.eq("tenant_id", pdChatUser.getTenantId());
            }
            if (pdChatUser.getWideType() != null) {
                queryWrapper.eq("wide_type", pdChatUser.getWideType());
            }
            if (pdChatUser.getUserName() != null) {
                queryWrapper.like("user_name", pdChatUser.getUserName());
            }
            if (pdChatUser.getSource() != null) {
                queryWrapper.eq("source", pdChatUser.getSource());
            }
            if (pdChatUser.getKeyId() != null) {
                queryWrapper.eq("key_id", pdChatUser.getKeyId());
            }
        }

        // 仅按引流日期倒序排序，不添加其他排序条件
        queryWrapper.orderByDesc("diver_data");

        // 执行分页查询
        return this.page(page, queryWrapper);
    }

}
