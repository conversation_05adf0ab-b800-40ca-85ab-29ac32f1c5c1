package org.jeecg.modules.corp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 中国省份经纬度枚举类
 * 包含全国34个省级行政区的中心坐标信息
 * 坐标数据与数据大屏地图的省份圆环标记点位置完全对应
 *
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Getter
@AllArgsConstructor
public enum ProvinceCoordinateEnum {

    // 直辖市
    BEIJING("北京市", "110000", 116.41995, 40.18994, Arrays.asList(
            new CityCoordinate("东城区", "110101", 116.418757, 39.917544),
            new CityCoordinate("西城区", "110102", 116.366794, 39.915309),
            new CityCoordinate("朝阳区", "110105", 116.443108, 39.921489),
            new CityCoordinate("丰台区", "110106", 116.286968, 39.858427),
            new CityCoordinate("石景山区", "110107", 116.195445, 39.914601),
            new CityCoordinate("海淀区", "110108", 116.298056, 39.959912),
            new CityCoordinate("门头沟区", "110109", 116.105381, 39.937183),
            new CityCoordinate("房山区", "110111", 115.988654, 39.735535),
            new CityCoordinate("通州区", "110112", 116.658603, 39.902486),
            new CityCoordinate("顺义区", "110113", 116.653525, 40.128936),
            new CityCoordinate("昌平区", "110114", 116.235906, 40.218085),
            new CityCoordinate("大兴区", "110115", 116.338033, 39.728908),
            new CityCoordinate("怀柔区", "110116", 116.637122, 40.324272),
            new CityCoordinate("平谷区", "110117", 117.112335, 40.144783),
            new CityCoordinate("密云区", "110118", 116.843352, 40.377362),
            new CityCoordinate("延庆区", "110119", 115.985006, 40.465325)
    )),

    TIANJIN("天津市", "120000", 117.347043, 39.288036, Arrays.asList(
            new CityCoordinate("和平区", "120101", 117.195907, 39.118327),
            new CityCoordinate("河东区", "120102", 117.226568, 39.122125),
            new CityCoordinate("河西区", "120103", 117.223372, 39.101563),
            new CityCoordinate("南开区", "120104", 117.164143, 39.120474),
            new CityCoordinate("河北区", "120105", 117.201569, 39.156632),
            new CityCoordinate("红桥区", "120106", 117.165894, 39.175066),
            new CityCoordinate("东丽区", "120110", 117.314040, 39.087764),
            new CityCoordinate("西青区", "120111", 117.012247, 39.139446),
            new CityCoordinate("津南区", "120112", 117.396632, 38.989577),
            new CityCoordinate("北辰区", "120113", 117.134801, 39.225555),
            new CityCoordinate("武清区", "120114", 117.044387, 39.384119),
            new CityCoordinate("宝坻区", "120115", 117.308094, 39.717989),
            new CityCoordinate("滨海新区", "120116", 117.654173, 39.032846),
            new CityCoordinate("宁河区", "120117", 117.824429, 39.328886),
            new CityCoordinate("静海区", "120118", 116.925304, 38.935671),
            new CityCoordinate("蓟州区", "120119", 117.407449, 40.045342)
    )),

    SHANGHAI("上海市", "310000", 121.438737, 31.072559, Arrays.asList(
            new CityCoordinate("黄浦区", "310101", 121.484443, 31.231763),
            new CityCoordinate("徐汇区", "310104", 121.436525, 31.188523),
            new CityCoordinate("长宁区", "310105", 121.424624, 31.220367),
            new CityCoordinate("静安区", "310106", 121.448224, 31.229003),
            new CityCoordinate("普陀区", "310107", 121.395514, 31.249162),
            new CityCoordinate("虹口区", "310109", 121.504132, 31.264718),
            new CityCoordinate("杨浦区", "310110", 121.526443, 31.259141),
            new CityCoordinate("闵行区", "310112", 121.381709, 31.112701),
            new CityCoordinate("宝山区", "310113", 121.489934, 31.398896),
            new CityCoordinate("嘉定区", "310114", 121.250333, 31.383524),
            new CityCoordinate("浦东新区", "310115", 121.567706, 31.245944),
            new CityCoordinate("金山区", "310116", 121.330736, 30.724697),
            new CityCoordinate("松江区", "310117", 121.223543, 31.032988),
            new CityCoordinate("青浦区", "310118", 121.113021, 31.151209),
            new CityCoordinate("奉贤区", "310120", 121.458472, 30.912345),
            new CityCoordinate("崇明区", "310151", 121.397516, 31.626946)
    )),

    CHONGQING("重庆市", "500000", 107.8839, 30.067297, Arrays.asList(
            new CityCoordinate("万州区", "500101", 108.380246, 30.807807),
            new CityCoordinate("涪陵区", "500102", 107.394905, 29.703652),
            new CityCoordinate("渝中区", "500103", 106.569865, 29.556742),
            new CityCoordinate("大渡口区", "500104", 106.482346, 29.484527),
            new CityCoordinate("江北区", "500105", 106.574271, 29.606703),
            new CityCoordinate("沙坪坝区", "500106", 106.456878, 29.541224),
            new CityCoordinate("九龙坡区", "500107", 106.511229, 29.502677),
            new CityCoordinate("南岸区", "500108", 106.644447, 29.523992),
            new CityCoordinate("北碚区", "500109", 106.437868, 29.825213),
            new CityCoordinate("綦江区", "500110", 106.651417, 29.028091),
            new CityCoordinate("大足区", "500111", 105.721733, 29.700498),
            new CityCoordinate("渝北区", "500112", 106.631187, 29.718142),
            new CityCoordinate("巴南区", "500113", 106.540256, 29.402298),
            new CityCoordinate("黔江区", "500114", 108.770677, 29.533609),
            new CityCoordinate("长寿区", "500115", 107.081276, 29.833671),
            new CityCoordinate("江津区", "500116", 106.253156, 29.283387)
    )),

    // 华北地区
    HEBEI("河北省", "130000", 114.502461, 38.045474, Arrays.asList(
            new CityCoordinate("石家庄市", "130100", 114.502461, 38.045474),
            new CityCoordinate("唐山市", "130200", 118.175393, 39.635113),
            new CityCoordinate("秦皇岛市", "130300", 119.586579, 39.942531),
            new CityCoordinate("邯郸市", "130400", 114.490686, 36.612273),
            new CityCoordinate("邢台市", "130500", 114.508851, 37.0682),
            new CityCoordinate("保定市", "130600", 115.482331, 38.867657),
            new CityCoordinate("张家口市", "130700", 114.884091, 40.811901),
            new CityCoordinate("承德市", "130800", 117.939152, 40.976204),
            new CityCoordinate("沧州市", "130900", 116.857461, 38.310582),
            new CityCoordinate("廊坊市", "131000", 116.713873, 39.529244),
            new CityCoordinate("衡水市", "131100", 115.665993, 37.735097)
    )),

    SHANXI("山西省", "140000", 112.304436, 37.618179, Arrays.asList(
            new CityCoordinate("太原市", "140100", 112.549248, 37.857014),
            new CityCoordinate("大同市", "140200", 113.295259, 40.09031),
            new CityCoordinate("阳泉市", "140300", 113.583285, 37.861188),
            new CityCoordinate("长治市", "140400", 113.113556, 36.191112),
            new CityCoordinate("晋城市", "140500", 112.851274, 35.497553),
            new CityCoordinate("朔州市", "140600", 112.433387, 39.331261),
            new CityCoordinate("晋中市", "140700", 112.736465, 37.696495),
            new CityCoordinate("运城市", "140800", 111.003957, 35.022778),
            new CityCoordinate("忻州市", "140900", 112.733538, 38.41769),
            new CityCoordinate("临汾市", "141000", 111.517973, 36.08415),
            new CityCoordinate("吕梁市", "141100", 111.134335, 37.524366)
    )),

    INNER_MONGOLIA("内蒙古自治区", "150000", 114.077429, 44.331087, Arrays.asList(
            new CityCoordinate("呼和浩特市", "150100", 111.670801, 40.818311),
            new CityCoordinate("包头市", "150200", 109.840405, 40.658168),
            new CityCoordinate("乌海市", "150300", 106.825563, 39.673734),
            new CityCoordinate("赤峰市", "150400", 118.956806, 42.275317),
            new CityCoordinate("通辽市", "150500", 122.263119, 43.617429),
            new CityCoordinate("鄂尔多斯市", "150600", 109.99029, 39.817179),
            new CityCoordinate("呼伦贝尔市", "150700", 119.758168, 49.215333),
            new CityCoordinate("巴彦淖尔市", "150800", 107.416959, 40.757402),
            new CityCoordinate("乌兰察布市", "150900", 113.114543, 41.034134),
            new CityCoordinate("兴安盟", "152200", 122.070317, 46.076268),
            new CityCoordinate("锡林郭勒盟", "152500", 116.090996, 43.944018),
            new CityCoordinate("阿拉善盟", "152900", 105.706422, 38.844814)
    )),

    // 东北地区
    LIAONING("辽宁省", "210000", 122.604994, 41.299712, Arrays.asList(
            new CityCoordinate("沈阳市", "210100", 123.429096, 41.796767),
            new CityCoordinate("大连市", "210200", 121.618622, 38.91459),
            new CityCoordinate("鞍山市", "210300", 122.995632, 41.110626),
            new CityCoordinate("抚顺市", "210400", 123.921109, 41.875956),
            new CityCoordinate("本溪市", "210500", 123.770519, 41.297909),
            new CityCoordinate("丹东市", "210600", 124.383044, 40.124296),
            new CityCoordinate("锦州市", "210700", 121.135742, 41.119269),
            new CityCoordinate("营口市", "210800", 122.235151, 40.667432),
            new CityCoordinate("阜新市", "210900", 121.648962, 42.011796),
            new CityCoordinate("辽阳市", "211000", 123.18152, 41.269402),
            new CityCoordinate("盘锦市", "211100", 122.06957, 41.124484),
            new CityCoordinate("铁岭市", "211200", 123.844279, 42.290585),
            new CityCoordinate("朝阳市", "211300", 120.451176, 41.576758),
            new CityCoordinate("葫芦岛市", "211400", 120.856394, 40.755572)
    )),

    JILIN("吉林省", "220000", 126.171208, 43.703954, Arrays.asList(
            new CityCoordinate("长春市", "220100", 125.3245, 43.886841),
            new CityCoordinate("吉林市", "220200", 126.55302, 43.843577),
            new CityCoordinate("四平市", "220300", 124.370785, 43.170344),
            new CityCoordinate("辽源市", "220400", 125.145349, 42.902692),
            new CityCoordinate("通化市", "220500", 125.936501, 41.721177),
            new CityCoordinate("白山市", "220600", 126.427839, 41.939994),
            new CityCoordinate("松原市", "220700", 124.823608, 45.118243),
            new CityCoordinate("白城市", "220800", 122.841114, 45.619026),
            new CityCoordinate("延边朝鲜族自治州", "222400", 129.513228, 42.904823)
    )),

    HEILONGJIANG("黑龙江省", "230000", 127.693027, 48.040465, Arrays.asList(
            new CityCoordinate("哈尔滨市", "230100", 126.642464, 45.756967),
            new CityCoordinate("齐齐哈尔市", "230200", 123.95792, 47.342081),
            new CityCoordinate("鸡西市", "230300", 130.975966, 45.300046),
            new CityCoordinate("鹤岗市", "230400", 130.277487, 47.332085),
            new CityCoordinate("双鸭山市", "230500", 131.157304, 46.643442),
            new CityCoordinate("大庆市", "230600", 125.11272, 46.590734),
            new CityCoordinate("伊春市", "230700", 128.899396, 47.724775),
            new CityCoordinate("佳木斯市", "230800", 130.361634, 46.809606),
            new CityCoordinate("七台河市", "230900", 131.015584, 45.771266),
            new CityCoordinate("牡丹江市", "231000", 129.618602, 44.582962),
            new CityCoordinate("黑河市", "231100", 127.499023, 50.249585),
            new CityCoordinate("绥化市", "231200", 126.99293, 46.637393),
            new CityCoordinate("大兴安岭地区", "232700", 124.711526, 52.335262)
    )),

    // 华东地区
    JIANGSU("江苏省", "320000", 119.486506, 32.983991, Arrays.asList(
            new CityCoordinate("南京市", "320100", 118.767413, 32.041544),
            new CityCoordinate("无锡市", "320200", 120.301663, 31.574729),
            new CityCoordinate("徐州市", "320300", 117.184811, 34.261792),
            new CityCoordinate("常州市", "320400", 119.946973, 31.772752),
            new CityCoordinate("苏州市", "320500", 120.619585, 31.299379),
            new CityCoordinate("南通市", "320600", 120.864608, 32.016212),
            new CityCoordinate("连云港市", "320700", 119.178821, 34.600018),
            new CityCoordinate("淮安市", "320800", 119.021265, 33.597506),
            new CityCoordinate("盐城市", "320900", 120.139998, 33.377631),
            new CityCoordinate("扬州市", "321000", 119.421003, 32.393159),
            new CityCoordinate("镇江市", "321100", 119.452753, 32.204402),
            new CityCoordinate("泰州市", "321200", 119.915176, 32.484882),
            new CityCoordinate("宿迁市", "321300", 118.275162, 33.963008)
    )),

    ZHEJIANG("浙江省", "330000", 120.109913, 29.181466, Arrays.asList(
            new CityCoordinate("杭州市", "330100", 120.153576, 30.287459),
            new CityCoordinate("宁波市", "330200", 121.549792, 29.868388),
            new CityCoordinate("温州市", "330300", 120.672111, 28.000575),
            new CityCoordinate("嘉兴市", "330400", 120.750865, 30.762653),
            new CityCoordinate("湖州市", "330500", 120.102398, 30.867198),
            new CityCoordinate("绍兴市", "330600", 120.582112, 29.997117),
            new CityCoordinate("金华市", "330700", 119.649506, 29.089524),
            new CityCoordinate("衢州市", "330800", 118.87263, 28.941708),
            new CityCoordinate("舟山市", "330900", 122.207216, 29.985295),
            new CityCoordinate("台州市", "331000", 121.428599, 28.661378),
            new CityCoordinate("丽水市", "331100", 119.921786, 28.451993)
    )),

    ANHUI("安徽省", "340000", 117.226884, 31.849254, Arrays.asList(
            new CityCoordinate("合肥市", "340100", 117.283042, 31.86119),
            new CityCoordinate("芜湖市", "340200", 118.376451, 31.326319),
            new CityCoordinate("蚌埠市", "340300", 117.363228, 32.916287),
            new CityCoordinate("淮南市", "340400", 117.018329, 32.647574),
            new CityCoordinate("马鞍山市", "340500", 118.507906, 31.689362),
            new CityCoordinate("淮北市", "340600", 116.794664, 33.971707),
            new CityCoordinate("铜陵市", "340700", 117.816576, 30.929935),
            new CityCoordinate("安庆市", "340800", 117.043551, 30.50883),
            new CityCoordinate("黄山市", "341000", 118.317325, 29.709239),
            new CityCoordinate("滁州市", "341100", 118.316264, 32.317351),
            new CityCoordinate("阜阳市", "341200", 115.819729, 32.896969),
            new CityCoordinate("宿州市", "341300", 116.984084, 33.633891),
            new CityCoordinate("六安市", "341500", 116.507676, 31.752889),
            new CityCoordinate("亳州市", "341600", 115.782939, 33.869338),
            new CityCoordinate("池州市", "341700", 117.489157, 30.656037),
            new CityCoordinate("宣城市", "341800", 118.757995, 30.945667)
    )),

    FUJIAN("福建省", "350000", 118.006468, 26.069925, Arrays.asList(
            new CityCoordinate("福州市", "350100", 119.200519, 26.047886),
            new CityCoordinate("厦门市", "350200", 118.123854, 24.676398),
            new CityCoordinate("莆田市", "350300", 118.894712, 25.445304),
            new CityCoordinate("三明市", "350400", 117.400007, 26.298093),
            new CityCoordinate("泉州市", "350500", 118.267651, 25.202187),
            new CityCoordinate("漳州市", "350600", 117.458578, 24.330766),
            new CityCoordinate("南平市", "350700", 118.147051, 27.338631),
            new CityCoordinate("龙岩市", "350800", 116.74379, 25.291574),
            new CityCoordinate("宁德市", "350900", 119.489399, 26.971518)
    )),

    JIANGXI("江西省", "360000", 115.732975, 27.636112, Arrays.asList(
            new CityCoordinate("南昌市", "360100", 115.892151, 28.676493),
            new CityCoordinate("景德镇市", "360200", 117.214664, 29.29256),
            new CityCoordinate("萍乡市", "360300", 113.852186, 27.622946),
            new CityCoordinate("九江市", "360400", 115.992811, 29.712034),
            new CityCoordinate("新余市", "360500", 114.930835, 27.810834),
            new CityCoordinate("鹰潭市", "360600", 117.033838, 28.238638),
            new CityCoordinate("赣州市", "360700", 114.940278, 25.85097),
            new CityCoordinate("吉安市", "360800", 114.986373, 27.111699),
            new CityCoordinate("宜春市", "360900", 114.391136, 27.8043),
            new CityCoordinate("抚州市", "361000", 116.358351, 27.98385),
            new CityCoordinate("上饶市", "361100", 117.971185, 28.44442)
    )),

    SHANDONG("山东省", "370000", 118.187759, 36.376092, Arrays.asList(
            new CityCoordinate("济南市", "370100", 117.000923, 36.675807),
            new CityCoordinate("青岛市", "370200", 120.355173, 36.082982),
            new CityCoordinate("淄博市", "370300", 118.047648, 36.814939),
            new CityCoordinate("枣庄市", "370400", 117.557964, 34.856424),
            new CityCoordinate("东营市", "370500", 118.66471, 37.434751),
            new CityCoordinate("烟台市", "370600", 121.391382, 37.539297),
            new CityCoordinate("潍坊市", "370700", 119.107078, 36.70925),
            new CityCoordinate("济宁市", "370800", 116.587245, 35.415393),
            new CityCoordinate("泰安市", "370900", 117.129063, 36.194968),
            new CityCoordinate("威海市", "371000", 122.116394, 37.513068),
            new CityCoordinate("日照市", "371100", 119.461208, 35.428588),
            new CityCoordinate("临沂市", "371300", 118.326443, 35.065282),
            new CityCoordinate("德州市", "371400", 116.307428, 37.453968),
            new CityCoordinate("聊城市", "371500", 115.980367, 36.456013),
            new CityCoordinate("滨州市", "371600", 118.016974, 37.383542),
            new CityCoordinate("菏泽市", "371700", 115.469381, 35.246531)
    )),

    // 华中地区
    HENAN("河南省", "410000", 113.619717, 33.902648, Arrays.asList(
            new CityCoordinate("郑州市", "410100", 113.665412, 34.757975),
            new CityCoordinate("开封市", "410200", 114.341447, 34.797049),
            new CityCoordinate("洛阳市", "410300", 112.434468, 34.663041),
            new CityCoordinate("平顶山市", "410400", 113.307718, 33.735241),
            new CityCoordinate("安阳市", "410500", 114.352482, 36.103442),
            new CityCoordinate("鹤壁市", "410600", 114.295444, 35.748236),
            new CityCoordinate("新乡市", "410700", 113.883991, 35.302616),
            new CityCoordinate("焦作市", "410800", 113.238266, 35.23904),
            new CityCoordinate("濮阳市", "410900", 115.041299, 35.768234),
            new CityCoordinate("许昌市", "411000", 113.826063, 34.022956),
            new CityCoordinate("漯河市", "411100", 114.026405, 33.575855),
            new CityCoordinate("三门峡市", "411200", 111.194099, 34.777338),
            new CityCoordinate("南阳市", "411300", 112.540918, 32.999082),
            new CityCoordinate("商丘市", "411400", 115.650497, 34.437054),
            new CityCoordinate("信阳市", "411500", 114.075031, 32.123274),
            new CityCoordinate("周口市", "411600", 114.649653, 33.620357),
            new CityCoordinate("驻马店市", "411700", 114.024736, 32.980169),
            new CityCoordinate("济源市", "419001", 112.590047, 35.090378)
    )),

    HUBEI("湖北省", "420000", 112.271301, 30.987527, Arrays.asList(
            new CityCoordinate("武汉市", "420100", 114.298572, 30.584355),
            new CityCoordinate("黄石市", "420200", 115.077048, 30.220074),
            new CityCoordinate("十堰市", "420300", 110.787916, 32.646907),
            new CityCoordinate("宜昌市", "420500", 111.290843, 30.702636),
            new CityCoordinate("襄阳市", "420600", 112.144146, 32.042426),
            new CityCoordinate("鄂州市", "420700", 114.890593, 30.396536),
            new CityCoordinate("荆门市", "420800", 112.204251, 31.03542),
            new CityCoordinate("孝感市", "420900", 113.926655, 30.926423),
            new CityCoordinate("荆州市", "421000", 112.23813, 30.326857),
            new CityCoordinate("黄冈市", "421100", 114.879365, 30.447711),
            new CityCoordinate("咸宁市", "421200", 114.328963, 29.832798),
            new CityCoordinate("随州市", "421300", 113.37377, 31.717497),
            new CityCoordinate("恩施土家族苗族自治州", "422800", 109.486948, 30.283114),
            new CityCoordinate("仙桃市", "429004", 113.453974, 30.364953),
            new CityCoordinate("潜江市", "429005", 112.896866, 30.421215),
            new CityCoordinate("天门市", "429006", 113.165862, 30.653061),
            new CityCoordinate("神农架林区", "429021", 110.671525, 31.744449)
    )),

    HUNAN("湖南省", "430000", 111.711649, 27.629216, Arrays.asList(
            new CityCoordinate("长沙市", "430100", 112.982279, 28.19409),
            new CityCoordinate("株洲市", "430200", 113.151737, 27.835806),
            new CityCoordinate("湘潭市", "430300", 112.944052, 27.82973),
            new CityCoordinate("衡阳市", "430400", 112.607693, 26.900358),
            new CityCoordinate("邵阳市", "430500", 111.469156, 27.237842),
            new CityCoordinate("岳阳市", "430600", 113.132855, 29.37029),
            new CityCoordinate("常德市", "430700", 111.691347, 29.040225),
            new CityCoordinate("张家界市", "430800", 110.479921, 29.127401),
            new CityCoordinate("益阳市", "430900", 112.355042, 28.570066),
            new CityCoordinate("郴州市", "431000", 113.032067, 25.793589),
            new CityCoordinate("永州市", "431100", 111.608019, 26.434516),
            new CityCoordinate("怀化市", "431200", 109.97824, 27.550082),
            new CityCoordinate("娄底市", "431300", 112.008497, 27.728136),
            new CityCoordinate("湘西土家族苗族自治州", "433100", 109.739735, 28.314296)
    )),

    // 华南地区
    GUANGDONG("广东省", "440000", 113.429919, 23.334643, Arrays.asList(
            new CityCoordinate("广州市", "440100", 113.280637, 23.125178),
            new CityCoordinate("韶关市", "440200", 113.591544, 24.801322),
            new CityCoordinate("深圳市", "440300", 114.085947, 22.547),
            new CityCoordinate("珠海市", "440400", 113.553986, 22.224979),
            new CityCoordinate("汕头市", "440500", 116.708463, 23.37102),
            new CityCoordinate("佛山市", "440600", 113.122717, 23.028762),
            new CityCoordinate("江门市", "440700", 113.094942, 22.590431),
            new CityCoordinate("湛江市", "440800", 110.364977, 21.274898),
            new CityCoordinate("茂名市", "440900", 110.919229, 21.659751),
            new CityCoordinate("肇庆市", "441200", 112.472529, 23.051546),
            new CityCoordinate("惠州市", "441300", 114.412599, 23.079404),
            new CityCoordinate("梅州市", "441400", 116.117582, 24.299112),
            new CityCoordinate("汕尾市", "441500", 115.364238, 22.774485),
            new CityCoordinate("河源市", "441600", 114.697802, 23.746266),
            new CityCoordinate("阳江市", "441700", 111.975107, 21.859222),
            new CityCoordinate("清远市", "441800", 113.051227, 23.685022),
            new CityCoordinate("东莞市", "441900", 113.746262, 23.046237),
            new CityCoordinate("中山市", "442000", 113.382391, 22.521113),
            new CityCoordinate("潮州市", "445100", 116.632301, 23.661701),
            new CityCoordinate("揭阳市", "445200", 116.355733, 23.543778),
            new CityCoordinate("云浮市", "445300", 112.044439, 22.929801)
    )),

    GUANGXI("广西壮族自治区", "450000", 108.7944, 23.833381, Arrays.asList(
            new CityCoordinate("南宁市", "450100", 108.320004, 22.82402),
            new CityCoordinate("柳州市", "450200", 109.411703, 24.314617),
            new CityCoordinate("桂林市", "450300", 110.299121, 25.274215),
            new CityCoordinate("梧州市", "450400", 111.297604, 23.474803),
            new CityCoordinate("北海市", "450500", 109.119254, 21.473343),
            new CityCoordinate("防城港市", "450600", 108.345478, 21.614631),
            new CityCoordinate("钦州市", "450700", 108.624175, 21.967127),
            new CityCoordinate("贵港市", "450800", 109.602146, 23.0936),
            new CityCoordinate("玉林市", "450900", 110.154393, 22.63136),
            new CityCoordinate("百色市", "451000", 106.616285, 23.897742),
            new CityCoordinate("贺州市", "451100", 111.552056, 24.414141),
            new CityCoordinate("河池市", "451200", 108.062105, 24.695899),
            new CityCoordinate("来宾市", "451300", 109.229772, 23.733766),
            new CityCoordinate("崇左市", "451400", 107.353926, 22.404108)
    )),

    HAINAN("海南省", "460000", 109.754859, 19.189767, Arrays.asList(
            new CityCoordinate("海口市", "460100", 110.33119, 20.031971),
            new CityCoordinate("三亚市", "460200", 109.508268, 18.247872),
            new CityCoordinate("三沙市", "460300", 112.348794, 16.831039),
            new CityCoordinate("儋州市", "460400", 109.576782, 19.517486),
            new CityCoordinate("五指山市", "469001", 109.516662, 18.776921),
            new CityCoordinate("琼海市", "469002", 110.466785, 19.246011),
            new CityCoordinate("文昌市", "469005", 110.753975, 19.612986),
            new CityCoordinate("万宁市", "469006", 110.388793, 18.796216),
            new CityCoordinate("东方市", "469007", 108.653789, 19.10198),
            new CityCoordinate("定安县", "469021", 110.349235, 19.684966),
            new CityCoordinate("屯昌县", "469022", 110.102773, 19.362916),
            new CityCoordinate("澄迈县", "469023", 110.007147, 19.737097),
            new CityCoordinate("临高县", "469024", 109.687697, 19.908293),
            new CityCoordinate("白沙黎族自治县", "469025", 109.452606, 19.224584),
            new CityCoordinate("昌江黎族自治县", "469026", 109.053351, 19.260968),
            new CityCoordinate("乐东黎族自治县", "469027", 109.175444, 18.74758),
            new CityCoordinate("陵水黎族自治县", "469028", 110.037218, 18.505006),
            new CityCoordinate("保亭黎族苗族自治县", "469029", 109.70245, 18.636371),
            new CityCoordinate("琼中黎族苗族自治县", "469030", 109.838389, 19.03557)
    )),

    // 西南地区
    SICHUAN("四川省", "510000", 102.693453, 30.674545, Arrays.asList(
            new CityCoordinate("成都市", "510100", 104.065735, 30.659462),
            new CityCoordinate("自贡市", "510300", 104.773447, 29.352765),
            new CityCoordinate("攀枝花市", "510400", 101.718637, 26.582347),
            new CityCoordinate("泸州市", "510500", 105.443348, 28.889138),
            new CityCoordinate("德阳市", "510600", 104.37898, 31.127991),
            new CityCoordinate("绵阳市", "510700", 104.741722, 31.46402),
            new CityCoordinate("广元市", "510800", 105.829757, 32.433668),
            new CityCoordinate("遂宁市", "510900", 105.571331, 30.513311),
            new CityCoordinate("内江市", "511000", 105.066138, 29.58708),
            new CityCoordinate("乐山市", "511100", 103.761263, 29.582024),
            new CityCoordinate("南充市", "511300", 106.082974, 30.795281),
            new CityCoordinate("眉山市", "511400", 103.831788, 30.048318),
            new CityCoordinate("宜宾市", "511500", 104.630825, 28.760189),
            new CityCoordinate("广安市", "511600", 106.633369, 30.456398),
            new CityCoordinate("达州市", "511700", 107.502262, 31.209484),
            new CityCoordinate("雅安市", "511800", 103.001033, 29.987722),
            new CityCoordinate("巴中市", "511900", 106.753669, 31.858809),
            new CityCoordinate("资阳市", "512000", 104.641917, 30.122211),
            new CityCoordinate("阿坝藏族羌族自治州", "513200", 102.221374, 31.899792),
            new CityCoordinate("甘孜藏族自治州", "513300", 101.963815, 30.050663),
            new CityCoordinate("凉山彝族自治州", "513400", 102.258746, 27.886762)
    )),

    GUIZHOU("贵州省", "520000", 106.880455, 26.826368, Arrays.asList(
            new CityCoordinate("贵阳市", "520100", 106.713478, 26.578343),
            new CityCoordinate("六盘水市", "520200", 104.846743, 26.584643),
            new CityCoordinate("遵义市", "520300", 106.937265, 27.706626),
            new CityCoordinate("安顺市", "520400", 105.932188, 26.245544),
            new CityCoordinate("毕节市", "520500", 105.28501, 27.301693),
            new CityCoordinate("铜仁市", "520600", 109.191555, 27.718346),
            new CityCoordinate("黔西南布依族苗族自治州", "522300", 104.897971, 25.08812),
            new CityCoordinate("黔东南苗族侗族自治州", "522600", 107.977488, 26.583352),
            new CityCoordinate("黔南布依族苗族自治州", "522700", 107.517156, 25.82329)
    )),

    YUNNAN("云南省", "530000", 101.485106, 25.008643, Arrays.asList(
            new CityCoordinate("昆明市", "530100", 102.712251, 25.040609),
            new CityCoordinate("曲靖市", "530300", 103.797851, 25.501557),
            new CityCoordinate("玉溪市", "530400", 102.543907, 24.350461),
            new CityCoordinate("保山市", "530500", 99.167133, 25.111802),
            new CityCoordinate("昭通市", "530600", 103.717216, 27.336999),
            new CityCoordinate("丽江市", "530700", 100.233026, 26.872108),
            new CityCoordinate("普洱市", "530800", 100.972344, 22.777321),
            new CityCoordinate("临沧市", "530900", 100.08697, 23.886567),
            new CityCoordinate("楚雄彝族自治州", "532300", 101.546046, 25.041988),
            new CityCoordinate("红河哈尼族彝族自治州", "532500", 103.384182, 23.366775),
            new CityCoordinate("文山壮族苗族自治州", "532600", 104.24401, 23.36951),
            new CityCoordinate("西双版纳傣族自治州", "532800", 100.797941, 22.001724),
            new CityCoordinate("大理白族自治州", "532900", 100.240382, 25.606486),
            new CityCoordinate("德宏傣族景颇族自治州", "533100", 98.578363, 24.436694),
            new CityCoordinate("怒江傈僳族自治州", "533300", 98.854304, 25.850949),
            new CityCoordinate("迪庆藏族自治州", "533400", 99.706463, 27.826853)
    )),

    TIBET("西藏自治区", "540000", 88.388277, 31.56375, Arrays.asList(
            new CityCoordinate("拉萨市", "540100", 91.132212, 29.660361),
            new CityCoordinate("日喀则市", "540200", 88.885148, 29.267519),
            new CityCoordinate("昌都市", "540300", 97.178452, 31.136875),
            new CityCoordinate("林芝市", "540400", 94.362348, 29.654693),
            new CityCoordinate("山南市", "540500", 91.766529, 29.236023),
            new CityCoordinate("那曲市", "540600", 92.060214, 31.476004),
            new CityCoordinate("阿里地区", "542500", 80.105498, 32.503187)
    )),

    // 西北地区
    SHAANXI("陕西省", "610000", 108.887114, 35.263661, Arrays.asList(
            new CityCoordinate("西安市", "610100", 108.948024, 34.263161),
            new CityCoordinate("铜川市", "610200", 108.979608, 34.916582),
            new CityCoordinate("宝鸡市", "610300", 107.14487, 34.369315),
            new CityCoordinate("咸阳市", "610400", 108.705117, 34.333439),
            new CityCoordinate("渭南市", "610500", 109.502882, 34.499381),
            new CityCoordinate("延安市", "610600", 109.40528, 36.596537),
            new CityCoordinate("汉中市", "610700", 107.028621, 33.077668),
            new CityCoordinate("榆林市", "610800", 109.741193, 38.290162),
            new CityCoordinate("安康市", "610900", 109.029273, 32.6903),
            new CityCoordinate("商洛市", "611000", 109.939776, 33.868319)
    )),

    GANSU("甘肃省", "620000", 103.823557, 36.058039, Arrays.asList(
            new CityCoordinate("兰州市", "620100", 103.823557, 36.058039),
            new CityCoordinate("嘉峪关市", "620200", 98.277304, 39.77313),
            new CityCoordinate("金昌市", "620300", 102.187888, 38.514238),
            new CityCoordinate("白银市", "620400", 104.173606, 36.54568),
            new CityCoordinate("天水市", "620500", 105.724998, 34.578529),
            new CityCoordinate("武威市", "620600", 102.634697, 37.929996),
            new CityCoordinate("张掖市", "620700", 100.455472, 38.932897),
            new CityCoordinate("平凉市", "620800", 106.684691, 35.54279),
            new CityCoordinate("酒泉市", "620900", 98.510795, 39.744023),
            new CityCoordinate("庆阳市", "621000", 107.638179, 35.734218),
            new CityCoordinate("定西市", "621100", 104.626294, 35.579578),
            new CityCoordinate("陇南市", "621200", 104.929379, 33.388598),
            new CityCoordinate("临夏回族自治州", "622900", 103.212006, 35.599446),
            new CityCoordinate("甘南藏族自治州", "623000", 102.911008, 34.986354)
    )),

    QINGHAI("青海省", "630000", 96.043533, 35.726403, Arrays.asList(
            new CityCoordinate("西宁市", "630100", 101.778916, 36.623178),
            new CityCoordinate("海东市", "630200", 102.10327, 36.502916),
            new CityCoordinate("海北藏族自治州", "632200", 100.901059, 36.959435),
            new CityCoordinate("黄南藏族自治州", "632300", 102.019988, 35.517744),
            new CityCoordinate("海南藏族自治州", "632500", 100.619542, 36.280353),
            new CityCoordinate("果洛藏族自治州", "632600", 100.242143, 34.4736),
            new CityCoordinate("玉树藏族自治州", "632700", 97.008522, 33.004049),
            new CityCoordinate("海西蒙古族藏族自治州", "632800", 97.370785, 37.374663)
    )),

    NINGXIA("宁夏回族自治区", "640000", 106.169866, 37.291332, Arrays.asList(
            new CityCoordinate("银川市", "640100", 106.278179, 38.46637),
            new CityCoordinate("石嘴山市", "640200", 106.376173, 39.01333),
            new CityCoordinate("吴忠市", "640300", 106.199409, 37.986165),
            new CityCoordinate("固原市", "640400", 106.285241, 36.004561),
            new CityCoordinate("中卫市", "640500", 105.189568, 37.514951)
    )),

    XINJIANG("新疆维吾尔自治区", "650000", 85.294711, 41.371801, Arrays.asList(
            new CityCoordinate("乌鲁木齐市", "650100", 87.617733, 43.792818),
            new CityCoordinate("克拉玛依市", "650200", 84.873946, 45.595886),
            new CityCoordinate("吐鲁番市", "650400", 89.184078, 42.947613),
            new CityCoordinate("哈密市", "650500", 93.51316, 42.833248),
            new CityCoordinate("昌吉回族自治州", "652300", 87.304012, 44.014577),
            new CityCoordinate("博尔塔拉蒙古自治州", "652700", 82.074778, 44.903258),
            new CityCoordinate("巴音郭楞蒙古自治州", "652800", 86.150969, 41.768552),
            new CityCoordinate("阿克苏地区", "652900", 80.265068, 41.170712),
            new CityCoordinate("克孜勒苏柯尔克孜自治州", "653000", 76.172825, 39.713431),
            new CityCoordinate("喀什地区", "653100", 75.989138, 39.467664),
            new CityCoordinate("和田地区", "653200", 79.92533, 37.110687),
            new CityCoordinate("伊犁哈萨克自治州", "654000", 81.317946, 43.92186),
            new CityCoordinate("塔城地区", "654200", 82.985732, 46.746301),
            new CityCoordinate("阿勒泰地区", "654300", 88.13963, 47.848393),
            new CityCoordinate("石河子市", "659001", 86.041075, 44.305886),
            new CityCoordinate("阿拉尔市", "659002", 81.285884, 40.541505),
            new CityCoordinate("图木舒克市", "659003", 79.077978, 39.867316),
            new CityCoordinate("五家渠市", "659004", 87.526884, 44.167401),
            new CityCoordinate("北屯市", "659005", 87.824932, 47.353177),
            new CityCoordinate("铁门关市", "659006", 85.672606, 41.827251),
            new CityCoordinate("双河市", "659007", 82.353656, 44.840524),
            new CityCoordinate("可克达拉市", "659008", 80.63579, 43.6832),
            new CityCoordinate("昆玉市", "659009", 79.287372, 37.207994),
            new CityCoordinate("胡杨河市", "659010", 84.827244, 44.697105)
    )),

    // 特别行政区
    HONG_KONG("香港特别行政区", "810000", 114.134357, 22.377366, Arrays.asList(
            new CityCoordinate("中西区", "810001", 114.154309, 22.284419),
            new CityCoordinate("湾仔区", "810002", 114.182373, 22.277749),
            new CityCoordinate("东区", "810003", 114.219375, 22.281608),
            new CityCoordinate("南区", "810004", 114.198067, 22.248831),
            new CityCoordinate("油尖旺区", "810005", 114.171203, 22.308228),
            new CityCoordinate("深水埗区", "810006", 114.159851, 22.330643),
            new CityCoordinate("九龙城区", "810007", 114.191785, 22.315804),
            new CityCoordinate("黄大仙区", "810008", 114.203761, 22.343552),
            new CityCoordinate("观塘区", "810009", 114.225067, 22.322313),
            new CityCoordinate("荃湾区", "810010", 114.117671, 22.369184),
            new CityCoordinate("屯门区", "810011", 113.976781, 22.391919),
            new CityCoordinate("元朗区", "810012", 114.024200, 22.445100),
            new CityCoordinate("北区", "810013", 114.148041, 22.499762),
            new CityCoordinate("大埔区", "810014", 114.171203, 22.445100),
            new CityCoordinate("沙田区", "810015", 114.188785, 22.379405),
            new CityCoordinate("西贡区", "810016", 114.266815, 22.314270),
            new CityCoordinate("离岛区", "810017", 113.944159, 22.287449)
    )),

    MACAO("澳门特别行政区", "820000", 113.566988, 22.159307, Arrays.asList(
            new CityCoordinate("澳门半岛", "820001", 113.549999, 22.198951),
            new CityCoordinate("氹仔", "820002", 113.565866, 22.159642),
            new CityCoordinate("路环", "820003", 113.559853, 22.122518)
    )),

    // 台湾地区
    TAIWAN("台湾省", "710000", 120.971485, 23.749452, Arrays.asList(
            new CityCoordinate("台北市", "710100", 121.565418, 25.032969),
            new CityCoordinate("高雄市", "710200", 120.666625, 22.61626),
            new CityCoordinate("基隆市", "710300", 121.739847, 25.130741),
            new CityCoordinate("台中市", "710400", 120.666625, 24.163162),
            new CityCoordinate("台南市", "710500", 120.206013, 22.997317),
            new CityCoordinate("新竹市", "710600", 120.968798, 24.806738),
            new CityCoordinate("嘉义市", "710700", 120.441569, 23.479718),
            new CityCoordinate("新北市", "710800", 121.465746, 25.012366),
            new CityCoordinate("宜兰县", "712200", 121.755573, 24.69295),
            new CityCoordinate("桃园市", "712300", 121.301080, 24.993877),
            new CityCoordinate("新竹县", "712400", 121.018397, 24.836927),
            new CityCoordinate("苗栗县", "712500", 120.820421, 24.560159),
            new CityCoordinate("彰化县", "712700", 120.516858, 24.051731),
            new CityCoordinate("南投县", "712800", 120.971485, 23.749452),
            new CityCoordinate("云林县", "712900", 120.543062, 23.709722),
            new CityCoordinate("嘉义县", "713000", 120.574692, 23.452775),
            new CityCoordinate("屏东县", "713300", 120.548828, 22.54951),
            new CityCoordinate("台东县", "713400", 121.108069, 22.755201),
            new CityCoordinate("花莲县", "713500", 121.606951, 23.993424),
            new CityCoordinate("澎湖县", "713600", 119.579996, 23.571156),
            new CityCoordinate("连江县", "713700", 119.539626, 26.197364),
            new CityCoordinate("金门县", "713800", 118.317257, 24.432706)
    ));

    /**
     * 省份名称
     */
    private final String provinceName;

    /**
     * 行政区划代码
     */
    private final String adcode;

    /**
     * 经度
     */
    private final Double longitude;

    /**
     * 纬度
     */
    private final Double latitude;

    /**
     * 下属城市列表
     */
    private final List<CityCoordinate> cities;

    /**
     * 根据省份名称查找枚举
     * @param provinceName 省份名称
     * @return 对应的枚举值，如果未找到返回null
     */
    public static ProvinceCoordinateEnum getByProvinceName(String provinceName) {
        if (provinceName == null || provinceName.trim().isEmpty()) {
            return null;
        }

        for (ProvinceCoordinateEnum province : values()) {
            if (province.getProvinceName().equals(provinceName) ||
                    province.getProvinceName().contains(provinceName) ||
                    provinceName.contains(province.getProvinceName().replace("省", "").replace("市", "").replace("自治区", "").replace("特别行政区", ""))) {
                return province;
            }
        }
        return null;
    }

    /**
     * 根据行政区划代码查找枚举
     * @param adcode 行政区划代码
     * @return 对应的枚举值，如果未找到返回null
     */
    public static ProvinceCoordinateEnum getByAdcode(String adcode) {
        if (adcode == null || adcode.trim().isEmpty()) {
            return null;
        }

        for (ProvinceCoordinateEnum province : values()) {
            if (province.getAdcode().equals(adcode)) {
                return province;
            }
        }
        return null;
    }

    /**
     * 模糊匹配省份名称
     * @param name 省份名称关键字
     * @return 对应的枚举值，如果未找到返回null
     */
    public static ProvinceCoordinateEnum fuzzyMatch(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        String cleanName = name.trim();

        for (ProvinceCoordinateEnum province : values()) {
            String provinceName = province.getProvinceName();

            // 精确匹配
            if (provinceName.equals(cleanName)) {
                return province;
            }

            // 去掉后缀匹配
            String shortName = provinceName.replace("省", "").replace("市", "")
                    .replace("自治区", "").replace("特别行政区", "");
            if (shortName.equals(cleanName) || cleanName.equals(shortName)) {
                return province;
            }

            // 包含匹配
            if (provinceName.contains(cleanName) || cleanName.contains(shortName)) {
                return province;
            }
        }

        return null;
    }

    /**
     * 根据城市名称查找所属省份
     * @param cityName 城市名称
     * @return 对应的省份枚举值，如果未找到返回null
     */
    public static ProvinceCoordinateEnum getByCityName(String cityName) {
        if (cityName == null || cityName.trim().isEmpty()) {
            return null;
        }

        for (ProvinceCoordinateEnum province : values()) {
            for (CityCoordinate city : province.getCities()) {
                if (city.getCityName().equals(cityName) ||
                        city.getCityName().contains(cityName) ||
                        cityName.contains(city.getCityName().replace("市", "").replace("区", "").replace("县", ""))) {
                    return province;
                }
            }
        }
        return null;
    }

    /**
     * 根据城市行政代码查找所属省份
     * @param cityAdcode 城市行政代码
     * @return 对应的省份枚举值，如果未找到返回null
     */
    public static ProvinceCoordinateEnum getByCityAdcode(String cityAdcode) {
        if (cityAdcode == null || cityAdcode.trim().isEmpty()) {
            return null;
        }

        for (ProvinceCoordinateEnum province : values()) {
            for (CityCoordinate city : province.getCities()) {
                if (city.getAdcode().equals(cityAdcode)) {
                    return province;
                }
            }
        }
        return null;
    }

    /**
     * 获取指定省份的城市坐标
     * @param cityName 城市名称
     * @return 城市坐标对象，如果未找到返回null
     */
    public CityCoordinate getCityCoordinate(String cityName) {
        if (cityName == null || cityName.trim().isEmpty()) {
            return null;
        }

        for (CityCoordinate city : this.cities) {
            if (city.getCityName().equals(cityName) ||
                    city.getCityName().contains(cityName) ||
                    cityName.contains(city.getCityName().replace("市", "").replace("区", "").replace("县", ""))) {
                return city;
            }
        }
        return null;
    }

    /**
     * 城市坐标内部类
     */
    @Getter
    @AllArgsConstructor
    public static class CityCoordinate {
        /**
         * 城市名称
         */
        private final String cityName;

        /**
         * 行政区划代码
         */
        private final String adcode;

        /**
         * 经度
         */
        private final Double longitude;

        /**
         * 纬度
         */
        private final Double latitude;
    }
}