package org.jeecg.modules.corp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class HourlyDto implements Serializable {

    @ApiModelProperty(value = "租户 id")
    private Integer tenantId;

    @ApiModelProperty(value = "比较类型:0-同比;1环比")
    private String compareMode;

    @ApiModelProperty(value = "查询日期(小时对比时使用)")
    private String orderDate;

    @ApiModelProperty(value = "对比日期(小时对比时使用)")
    private String compareDate;

    @ApiModelProperty(value = "开始时间(天/周/月汇总时使用)", example = "2025-06-01")
    private String startDate;

    @ApiModelProperty(value = "结束时间(天/周/月汇总时使用)", example = "2025-06-30")
    private String endDate;

    @ApiModelProperty(value = "对比开始时间(天/周/月汇总时使用)", example = "2025-05-01")
    private String compareStartDate;

    @ApiModelProperty(value = "对比结束时间(天/周/月汇总时使用)", example = "2025-05-30")
    private String compareEndDate;

    @ApiModelProperty(value = "对比类型:0-小时;1-天;2-周;3-月")
    private String compareType;

    @ApiModelProperty(value = "预约类型:0-车险;1-财险;2-增值服务")
    private String pdType;

}
