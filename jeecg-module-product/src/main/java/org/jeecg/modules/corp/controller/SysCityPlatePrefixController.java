package org.jeecg.modules.corp.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.corp.dto.CityConfigDTO;
import org.jeecg.modules.corp.dto.CityConfigSearchDTO;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 城市配置管理
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Api(tags="城市配置管理")
@RestController
@RequestMapping("/corp/cityConfig")
@Slf4j
public class SysCityPlatePrefixController extends JeecgController<SysCityPlatePrefix, ISysCityPlatePrefixService> {

    @Autowired
    private ISysCityPlatePrefixService sysCityPlatePrefixService;

    @Autowired
    private IEaRegionService eaRegionService;

    /**
     * 分页列表查询
     *
     * @param searchDTO
     * @return
     */
    @AutoLog(value = "城市配置-分页列表查询")
    @ApiOperation(value="城市配置-分页列表查询", notes="城市配置-分页列表查询")
    @PostMapping(value = "/list")
    public Result<IPage<SysCityPlatePrefix>> queryPageList(@RequestBody CityConfigSearchDTO searchDTO) {
        IPage<SysCityPlatePrefix> pageList = sysCityPlatePrefixService.searchCityConfigs(searchDTO);
        return Result.OK(pageList);
    }

    /**
     * 添加城市配置
     *
     * @param cityConfigDTO
     * @return
     */
    @AutoLog(value = "城市配置-添加")
    @ApiOperation(value="城市配置-添加", notes="城市配置-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@Valid @RequestBody CityConfigDTO cityConfigDTO) {
        try {
            boolean result = sysCityPlatePrefixService.addCityConfig(cityConfigDTO);
            if (result) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加城市配置失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑城市配置
     *
     * @param cityConfigDTO
     * @return
     */
    @AutoLog(value = "城市配置-编辑")
    @ApiOperation(value="城市配置-编辑", notes="城市配置-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Valid @RequestBody CityConfigDTO cityConfigDTO) {
        try {
            boolean result = sysCityPlatePrefixService.updateCityConfig(cityConfigDTO);
            if (result) {
                return Result.OK("编辑成功！");
            } else {
                return Result.error("编辑失败！");
            }
        } catch (Exception e) {
            log.error("编辑城市配置失败", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除城市配置
     *
     * @param id
     * @return
     */
    @AutoLog(value = "城市配置-通过id删除")
    @ApiOperation(value="城市配置-通过id删除", notes="城市配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            boolean result = sysCityPlatePrefixService.deleteCityConfig(id);
            if (result) {
                return Result.OK("删除成功!");
            } else {
                return Result.error("删除失败!");
            }
        } catch (Exception e) {
            log.error("删除城市配置失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除城市配置
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "城市配置-批量删除")
    @ApiOperation(value="城市配置-批量删除", notes="城市配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                sysCityPlatePrefixService.deleteCityConfig(id);
            }
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            log.error("批量删除城市配置失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id查询城市配置
     *
     * @param id
     * @return
     */
    @AutoLog(value = "城市配置-通过id查询")
    @ApiOperation(value="城市配置-通过id查询", notes="城市配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CityConfigDTO> queryById(@RequestParam(name="id",required=true) String id) {
        CityConfigDTO cityConfig = sysCityPlatePrefixService.getCityConfigById(id);
        if(cityConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(cityConfig);
    }



    /**
     * 获取上级城市列表（用于下拉选择）
     *
     * @return
     */
    @AutoLog(value = "城市配置-获取上级城市列表")
    @ApiOperation(value="城市配置-获取上级城市列表", notes="城市配置-获取上级城市列表")
    @GetMapping(value = "/getParentCities")
    public Result<List<EaRegion>> getParentCities() {
        // 获取所有一级和二级城市作为上级城市选项
        List<EaRegion> parentCities = eaRegionService.findProvinces();
        return Result.OK(parentCities);
    }

    /**
     * 同步城市数据
     *
     * @return
     */
    @AutoLog(value = "城市配置-同步城市数据")
    @ApiOperation(value="城市配置-同步城市数据", notes="城市配置-同步城市数据")
    @PostMapping(value = "/syncCityData")
    public Result<?> syncCityData() {
        try {
            boolean result = sysCityPlatePrefixService.syncCityData();
            if (result) {
                return Result.OK("同步城市数据成功！");
            } else {
                return Result.error("同步城市数据失败！");
            }
        } catch (Exception e) {
            log.error("同步城市数据失败", e);
            return Result.error("同步失败：" + e.getMessage());
        }
    }
}
