<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.PdCompanyMapper">

    <!-- 根据保司ID列表查询保司信息 -->
    <select id="queryByCompanyIds" resultType="org.jeecg.modules.corp.entity.PdCompany">
        SELECT
            id,
            name,
            icon_img
        FROM pd_company
        WHERE id IN
        <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        AND is_delete = '0'
        AND enable_type = 'Y'
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有保司信息 -->
    <select id="queryAllCompanies" resultType="org.jeecg.modules.corp.entity.PdCompany">
        SELECT
            id,
            name,
            icon_img
        FROM pd_company
        WHERE is_delete = '0'
        AND enable_type = 'Y'
        ORDER BY create_time DESC
    </select>

</mapper>