package org.jeecg.modules.corp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 省份统计查询DTO
 * 用于查询一级省份的预约统计数据
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "省份统计查询DTO", description = "省份统计查询参数")
public class ProvinceStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询日期
     */
    @ApiModelProperty(value = "查询日期", example = "2025-06-15")
    private String queryDate;

    /**
     * 租户ID（可选，为空则查询所有租户）
     */
    @ApiModelProperty(value = "租户ID", example = "1")
    private Integer tenantId;

    public ProvinceStatisticsDto() {
    }

    public ProvinceStatisticsDto(String queryDate) {
        this.queryDate = queryDate;
    }

    public ProvinceStatisticsDto(String queryDate, Integer tenantId) {
        this.queryDate = queryDate;
        this.tenantId = tenantId;
    }
}
