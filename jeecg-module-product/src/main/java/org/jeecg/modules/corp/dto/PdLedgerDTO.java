
package org.jeecg.modules.corp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
public class PdLedgerDTO {

    @ExcelProperty("险种")
    private String insuranceName;

    @ExcelProperty("签单日期")
    private String signDate;
    private Timestamp signDateTime;

    @ExcelProperty("投保人")
    private String policyholder;

    @ExcelProperty("被保人")
    private String insured;

    @ExcelProperty("手机号")
    private String phoneNumber;

    @ExcelProperty("车牌号")
    private String licensePlate;

    @ExcelProperty("厂牌型号")
    private String brandModel;

    @ExcelProperty("车架号")
    private String vin;

    @ExcelProperty("多租户")
    private Integer tenantId;
    /**是否存在聊天用户（0=否，1=是）*/
    private Integer hasChatUser;
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PdLedgerDTO)) return false;
        PdLedgerDTO that = (PdLedgerDTO) o;
        return licensePlate != null && licensePlate.equals(that.licensePlate)
                && tenantId != null && tenantId.equals(that.tenantId);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(licensePlate, tenantId);
    }
}
