package org.jeecg.modules.corp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.corp.dto.ClickDto;
import org.jeecg.modules.corp.dto.HourlyDto;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.vo.hour.ReportTotalVO;
import org.jeecg.modules.corp.vo.report.ClickReportHourlyVO;
import org.jeecg.modules.corp.vo.report.ReportColumnarVO;
import org.jeecg.modules.corp.vo.report.ReportCoordinateVO;
import org.jeecg.modules.corp.vo.report.RegionCoordinateVO;
import org.jeecg.modules.corp.vo.ProvinceStatisticsVO;
import org.jeecg.modules.corp.dto.ProvinceStatisticsDto;
import org.jeecg.modules.corp.vo.CityStatisticsVO;
import org.jeecg.modules.corp.dto.CityStatisticsDto;

import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Service
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
public interface IClickReportHourlyService extends IService<ClickReportHourly> {

    /**
     * 根据日期范围获取点击统计数据
     *
     * @param params 参数Map，包含startDate, endDate, configType, tenantId等参数
     * @return 统计数据列表
     */
    List<Map<String, Object>> getClickStatsByDateRange(Map<String, Object> params);

    /**
     * 新增按小时统计点击报表
     *
     * @param dto 参数
     */
    ClickReportHourly add(ClickReportHourly dto);

    /**
     * 修改按小时统计点击报表
     *
     * @param dto 参数
     */
    ClickReportHourly edit(ClickReportHourly dto);

    /**
     * 删除按小时统计点击报表
     *
     * @param id 主键
     */
    void deleteById(String id);

    /**
     * 分页查询按小时统计点击报表
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<ClickReportHourly> findPage(IPage<ClickReportHourly> page, ClickReportHourly dto);

    /**
     * 获取今日点击数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日点击数总和
     */
    Integer getTodayClickCount(Integer tenantId);

    /**
     * 根据租户ID添加点击数
     *
     * @param tenantId 租户ID
     */
    void addClickByTenantId(Integer tenantId);

    /**
     * 获取今日预约总数统计
     *
     * @param tenantId 租户ID，如果为0则统计所有租户
     * @return 今日预约总数
     */
    Integer getTodayReservationCount(Integer tenantId);

    /**
     * 获取数据大屏统计数据
     * @param dto
     * @return
     */
    ClickReportHourlyVO getScreenDate(ClickDto dto);

    List<ReportColumnarVO> getColumnarDate();

    ReportTotalVO getHourDate(HourlyDto dto);

    List<ReportCoordinateVO> reportCoordinateVO();

    /**
     * 获取地区坐标信息（总部+子城市结构）
     * @return 地区坐标信息
     */
    RegionCoordinateVO getRegionCoordinate();

    /**
     * 获取省份预约统计数据（包含城市code和经纬度）
     * @param dto 查询参数
     * @return 省份统计数据列表
     */
    List<ProvinceStatisticsVO> getProvinceReservationStatistics(ProvinceStatisticsDto dto);

    /**
     * 获取车险预约统计数据
     * @param dto 查询参数
     * @return 车险统计数据列表
     */
    List<ProvinceStatisticsVO> getCarInsuranceStatistics(ProvinceStatisticsDto dto);

    /**
     * 获取财险预约统计数据
     * @param dto 查询参数
     * @return 财险统计数据列表
     */
    List<ProvinceStatisticsVO> getPropertyInsuranceStatistics(ProvinceStatisticsDto dto);

    /**
     * 获取增值服务预约统计数据
     * @param dto 查询参数
     * @return 增值服务统计数据列表
     */
    List<ProvinceStatisticsVO> getValueAddedServiceStatistics(ProvinceStatisticsDto dto);

    /**
     * 获取所有一级省份的统计数据（多线程查询车险、财险、增值服务并合并）
     * @param dto 查询参数
     * @return 合并后的省份统计数据列表
     */
    List<ProvinceStatisticsVO> getProvinceStatisticsWithMultiThread(ProvinceStatisticsDto dto);

    /**
     * 根据省份code查询其所有子城市的预约总数
     * @param dto 查询参数（包含省份code）
     * @return 子城市预约统计数据列表
     */
    List<CityStatisticsVO> getCityStatisticsByProvinceCode(CityStatisticsDto dto);

}


