<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.jeecg.modules.corp.mapper.ClickReportHourlyMapper">
    <sql id="AllColumns">
        t.id as id,
        t.click_num as clickNum,
        t.stat_date as statDate,
        t.hour as hour,
        t.tenant_id as tenantId
    </sql>

    <!-- 获取省份预约统计数据（包含城市code和经纬度） -->
    <select id="getProvinceReservationStatistics" resultType="java.util.Map">
        SELECT
            city_stats.adcode,
            city_stats.name,
            city_stats.reservationCount
        FROM (
            SELECT
                COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')) as adcode,
                COALESCE(region.name, '未知省份') as name,
                COUNT(*) as reservationCount
            FROM (
                -- 车险数据
                SELECT
                    city_code_level,
                    create_time
                FROM pd_car_info
                WHERE city_code_level IS NOT NULL
                  AND DATE(create_time) = #{dto.queryDate}
                  <if test="dto.tenantId != null">
                    AND tenant_id = #{dto.tenantId}
                  </if>

                UNION ALL

                -- 财险数据
                SELECT
                    city_code_level,
                    create_time
                FROM pd_casualty_info
                WHERE city_code_level IS NOT NULL
                  AND DATE(create_time) = #{dto.queryDate}
                  <if test="dto.tenantId != null">
                    AND tenant_id = #{dto.tenantId}
                  </if>

                UNION ALL

                -- 增值服务数据
                SELECT
                    city_code_level,
                    create_time
                FROM pd_added
                WHERE city_code_level IS NOT NULL
                  AND DATE(create_time) = #{dto.queryDate}
                  <if test="dto.tenantId != null">
                    AND tenant_id = #{dto.tenantId}
                  </if>
            ) t
            LEFT JOIN ea_region region ON region.code = CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')
            GROUP BY
                COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')),
                COALESCE(region.name, '未知省份')
        ) city_stats
        ORDER BY city_stats.reservationCount DESC
    </select>

    <!-- 获取车险预约统计数据 -->
    <select id="getCarInsuranceStatistics" resultType="java.util.Map">
        SELECT
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')) as adcode,
            COALESCE(region.name, '未知省份') as name,
            COUNT(*) as reservationCount
        FROM pd_car_info t
        LEFT JOIN ea_region region ON region.code = CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')
        WHERE t.city_code_level IS NOT NULL
          AND DATE(t.create_time) = #{dto.queryDate}
          <if test="dto.tenantId != null">
            AND t.tenant_id = #{dto.tenantId}
          </if>
        GROUP BY
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')),
            COALESCE(region.name, '未知省份')
        ORDER BY reservationCount DESC
    </select>

    <!-- 获取财险预约统计数据 -->
    <select id="getPropertyInsuranceStatistics" resultType="java.util.Map">
        SELECT
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')) as adcode,
            COALESCE(region.name, '未知省份') as name,
            COUNT(*) as reservationCount
        FROM pd_casualty_info t
        LEFT JOIN ea_region region ON region.code = CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')
        WHERE t.city_code_level IS NOT NULL
          AND DATE(t.create_time) = #{dto.queryDate}
          <if test="dto.tenantId != null">
            AND t.tenant_id = #{dto.tenantId}
          </if>
        GROUP BY
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')),
            COALESCE(region.name, '未知省份')
        ORDER BY reservationCount DESC
    </select>

    <!-- 获取增值服务预约统计数据 -->
    <select id="getValueAddedServiceStatistics" resultType="java.util.Map">
        SELECT
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')) as adcode,
            COALESCE(region.name, '未知省份') as name,
            COUNT(*) as reservationCount
        FROM pd_added t
        LEFT JOIN ea_region region ON region.code = CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')
        WHERE t.city_code_level IS NOT NULL
          AND DATE(t.create_time) = #{dto.queryDate}
          <if test="dto.tenantId != null">
            AND t.tenant_id = #{dto.tenantId}
          </if>
        GROUP BY
            COALESCE(region.code, CONCAT(SUBSTRING(t.city_code_level, 1, 2), '0000')),
            COALESCE(region.name, '未知省份')
        ORDER BY reservationCount DESC
    </select>

</mapper>