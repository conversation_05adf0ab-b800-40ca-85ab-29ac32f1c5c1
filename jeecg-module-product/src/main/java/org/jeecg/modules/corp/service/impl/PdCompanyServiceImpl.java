package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.corp.entity.PdCompany;
import org.jeecg.modules.corp.mapper.PdCompanyMapper;
import org.jeecg.modules.corp.service.IPdCompanyService;
import org.jeecg.modules.system.entity.WlTenantInsuranceRel;
import org.jeecg.modules.system.service.IWlTenantInsuranceRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: app 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Service
public class PdCompanyServiceImpl extends ServiceImpl<PdCompanyMapper, PdCompany> implements IPdCompanyService {

    @Autowired
    private IWlTenantInsuranceRelService wlTenantInsuranceRelService;

    @Override
    public List<PdCompany> queryPageList() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantId = request.getHeader("tenant-id-link");

        // 如果租户ID存在，则查询租户与保司的关系表
        if (StrUtil.isNotEmpty(tenantId)) {
            // 根据租户ID查询关系表，获取保司ID列表
            List<WlTenantInsuranceRel> relList = wlTenantInsuranceRelService.getByTenantId(tenantId);

            if (relList != null && !relList.isEmpty()) {
                // 提取保司ID列表
                List<String> companyIds = relList.stream()
                        .map(WlTenantInsuranceRel::getInsuranceCompanyId)
                        .collect(Collectors.toList());

                // 根据保司ID列表查询保司信息
                List<PdCompany> companyList = this.baseMapper.queryByCompanyIds(companyIds);

                // 如果根据保司ID列表查询到了数据，则返回结果
                if (companyList != null && !companyList.isEmpty()) {
                    return companyList;
                }
            }
        }

        // 如果租户ID不存在或者根据租户ID没有查询出数据，则查询所有保司
        return this.baseMapper.queryAllCompanies();
    }
}
