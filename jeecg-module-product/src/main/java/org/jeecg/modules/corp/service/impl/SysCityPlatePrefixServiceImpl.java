package org.jeecg.modules.corp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.jeecg.modules.corp.dto.CityConfigDTO;
import org.jeecg.modules.corp.dto.CityConfigSearchDTO;
import org.jeecg.modules.corp.entity.SysCityPlatePrefix;
import org.jeecg.modules.corp.mapper.SysCityPlatePrefixMapper;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description: 城市车牌前缀关系表
 * @Author: jeecg-boot
 * @Date: 2024-11-12
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysCityPlatePrefixServiceImpl extends ServiceImpl<SysCityPlatePrefixMapper, SysCityPlatePrefix> implements ISysCityPlatePrefixService {

    @Autowired
    private IEaRegionService eaRegionService;

    @Override
    public String getPlatePrefixByCityCode(String cityCode) {
        return baseMapper.getPlatePrefixByCityCode(cityCode);
    }

    @Override
    public IPage<SysCityPlatePrefix> searchCityConfigs(CityConfigSearchDTO searchDTO) {
        Page<SysCityPlatePrefix> page = new Page<>(searchDTO.getPageNo(), searchDTO.getPageSize());
        QueryWrapper<SysCityPlatePrefix> queryWrapper = new QueryWrapper<>();

        // 添加搜索条件
        if (StringUtils.isNotBlank(searchDTO.getCityCode())) {
            queryWrapper.like("city_code", searchDTO.getCityCode());
        }
        if (StringUtils.isNotBlank(searchDTO.getAllCityName())) {
            queryWrapper.like("all_city_name", searchDTO.getAllCityName());
        }
        if (StringUtils.isNotBlank(searchDTO.getTopCityName())) {
            queryWrapper.like("top_city_name", searchDTO.getTopCityName());
        }
        if (StringUtils.isNotBlank(searchDTO.getPlatePrefix())) {
            queryWrapper.like("plate_prefix", searchDTO.getPlatePrefix());
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc("id");

        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addCityConfig(CityConfigDTO cityConfigDTO) {
        try {
            // 1. 检查车牌前缀表中是否已存在
            LambdaQueryWrapper<SysCityPlatePrefix> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysCityPlatePrefix::getCityCode, cityConfigDTO.getCityCode());
            SysCityPlatePrefix existingPrefix = this.getOne(queryWrapper);

            if (existingPrefix != null) {
                log.warn("城市车牌前缀已存在，城市编码: {}", cityConfigDTO.getCityCode());
                return false;
            }

            // 2. 检查EaRegion中是否存在该城市，如果不存在则添加
            EaRegion existingRegion = eaRegionService.findByCode(cityConfigDTO.getCityCode());
            if (existingRegion == null) {
                // 添加到EaRegion
                EaRegion newRegion = new EaRegion();
                newRegion.setCode(cityConfigDTO.getCityCode());
                newRegion.setName(cityConfigDTO.getRegionCityName()); // 使用地区表城市名称
                newRegion.setNodeType(3); // 默认三级城市
                newRegion.setCreateTime(new Date());
                newRegion.setCityLevel("3"); // 默认三级

                // 设置父节点
                if (StringUtils.isNotBlank(cityConfigDTO.getParentCityCode())) {
                    EaRegion parentRegion = eaRegionService.findByCode(cityConfigDTO.getParentCityCode());
                    if (parentRegion != null) {
                        newRegion.setParentId(parentRegion.getId());
                    } else {
                        log.warn("未找到上级城市，编码: {}", cityConfigDTO.getParentCityCode());
                    }
                }

                eaRegionService.save(newRegion);
                log.info("成功添加城市到EaRegion: {} (地区表名称: {})", cityConfigDTO.getAllCityName(), cityConfigDTO.getRegionCityName());
            }

            // 3. 添加车牌前缀记录
            SysCityPlatePrefix platePrefix = new SysCityPlatePrefix();
            platePrefix.setCityCode(cityConfigDTO.getCityCode());
            platePrefix.setAllCityName(cityConfigDTO.getAllCityName());
            platePrefix.setTopCityName(cityConfigDTO.getTopCityName());
            platePrefix.setPlatePrefix(cityConfigDTO.getPlatePrefix());

            this.save(platePrefix);
            return true;

        } catch (Exception e) {
            log.error("添加城市配置失败", e);
            throw new RuntimeException("添加城市配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCityConfig(CityConfigDTO cityConfigDTO) {
        try {
            // 1. 查找现有记录
            SysCityPlatePrefix existingPrefix = this.getById(cityConfigDTO.getId());
            if (existingPrefix == null) {
                log.warn("未找到要更新的城市配置，ID: {}", cityConfigDTO.getId());
                return false;
            }

            // 2. 更新EaRegion中的城市信息
            EaRegion region = eaRegionService.findByCode(existingPrefix.getCityCode());
            if (region != null) {
                region.setName(cityConfigDTO.getRegionCityName()); // 使用地区表城市名称

                // 如果上级城市编码发生变化，更新父级关系
                if (StringUtils.isNotBlank(cityConfigDTO.getParentCityCode())) {
                    EaRegion parentRegion = eaRegionService.findByCode(cityConfigDTO.getParentCityCode());
                    if (parentRegion != null) {
                        region.setParentId(parentRegion.getId());
                        log.info("更新父节点: {} -> {}", cityConfigDTO.getParentCityCode(), parentRegion.getId());
                    }
                }

                eaRegionService.updateById(region);
                log.info("成功更新EaRegion: {} (地区表名称: {})", cityConfigDTO.getAllCityName(), cityConfigDTO.getRegionCityName());
            }

            // 3. 更新车牌前缀记录
            existingPrefix.setAllCityName(cityConfigDTO.getAllCityName());
            existingPrefix.setTopCityName(cityConfigDTO.getTopCityName());
            existingPrefix.setPlatePrefix(cityConfigDTO.getPlatePrefix());

            this.updateById(existingPrefix);
            log.info("成功更新城市配置: {}", cityConfigDTO.getAllCityName());
            return true;

        } catch (Exception e) {
            log.error("更新城市配置失败", e);
            throw new RuntimeException("更新城市配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCityConfig(String id) {
        try {
            SysCityPlatePrefix platePrefix = this.getById(id);
            if (platePrefix == null) {
                log.warn("未找到要删除的城市配置，ID: {}", id);
                return false;
            }

            // 1. 删除EaRegion中的城市记录
            EaRegion region = eaRegionService.findByCode(platePrefix.getCityCode());
            if (region != null) {
                // 检查是否有子城市，如果有则不允许删除
                LambdaQueryWrapper<EaRegion> childQuery = new LambdaQueryWrapper<>();
                childQuery.eq(EaRegion::getParentId, region.getId());
                long childCount = eaRegionService.count(childQuery);

                if (childCount > 0) {
                    log.warn("该城市下还有子城市，不允许删除，城市编码: {}", platePrefix.getCityCode());
                    throw new RuntimeException("该城市下还有子城市，不允许删除");
                }

                eaRegionService.removeById(region.getId());
                log.info("成功删除EaRegion记录: {}", platePrefix.getAllCityName());
            }

            // 2. 删除车牌前缀记录
            this.removeById(id);
            log.info("成功删除城市配置: {}", platePrefix.getAllCityName());
            return true;

        } catch (Exception e) {
            log.error("删除城市配置失败", e);
            throw new RuntimeException("删除城市配置失败: " + e.getMessage());
        }
    }

    @Override
    public CityConfigDTO getCityConfigById(String id) {
        SysCityPlatePrefix platePrefix = this.getById(id);
        if (platePrefix == null) {
            return null;
        }

        CityConfigDTO dto = new CityConfigDTO();
        BeanUtils.copyProperties(platePrefix, dto);

        // 获取地区表城市信息和上级城市信息
        EaRegion region = eaRegionService.findByCode(platePrefix.getCityCode());
        if (region != null) {
            dto.setRegionCityName(region.getName()); // 设置地区表城市名称

            if (region.getParentId() != null && region.getParentId() > 0) {
                EaRegion parentRegion = eaRegionService.getById(region.getParentId());
                if (parentRegion != null) {
                    dto.setParentCityCode(parentRegion.getCode());
                    dto.setParentCityName(parentRegion.getName());
                }
            }
        }

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncCityData() {
        try {
            log.info("开始同步城市数据...");

            // 获取所有EaRegion中的二级城市（地级市）
            LambdaQueryWrapper<EaRegion> regionQuery = new LambdaQueryWrapper<>();
            regionQuery.eq(EaRegion::getNodeType, 2); // 二级城市
            List<EaRegion> cities = eaRegionService.list(regionQuery);

            int syncCount = 0;
            for (EaRegion city : cities) {
                // 检查是否已存在车牌前缀记录
                LambdaQueryWrapper<SysCityPlatePrefix> prefixQuery = new LambdaQueryWrapper<>();
                prefixQuery.eq(SysCityPlatePrefix::getCityCode, city.getCode());
                SysCityPlatePrefix existingPrefix = this.getOne(prefixQuery);

                if (existingPrefix == null) {
                    // 创建新的车牌前缀记录
                    SysCityPlatePrefix newPrefix = new SysCityPlatePrefix();
                    newPrefix.setCityCode(city.getCode());
                    newPrefix.setAllCityName(eaRegionService.getFullCityNameById(city.getId()));
                    newPrefix.setTopCityName(eaRegionService.getTopCityNameById(city.getId()));
                    // 车牌前缀需要手动设置，这里先设置为空
                    newPrefix.setPlatePrefix("");


                    this.save(newPrefix);
                    syncCount++;
                }
            }

            log.info("同步城市数据完成，新增记录数: {}", syncCount);
            return true;

        } catch (Exception e) {
            log.error("同步城市数据失败", e);
            throw new RuntimeException("同步城市数据失败: " + e.getMessage());
        }
    }
}
