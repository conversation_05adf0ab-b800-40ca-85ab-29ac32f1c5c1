package org.jeecg.modules.wh.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Data
@TableName("wh_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_record对象", description="用户门户记录表")
public class WhRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**租户ID*/
    @Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private Integer tenantId;

    /**记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录*/
    @Excel(name = "记录类型", width = 15, dicCode = "wh_record_type")
    @ApiModelProperty(value = "记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录")
    private Integer recordType;

    /**内容类型：1.保险产品 2.保险资讯 3.保险公司*/
    @Excel(name = "内容类型", width = 15, dicCode = "wh_content_type")
    @ApiModelProperty(value = "内容类型：1.保险产品 2.保险资讯 3.保险公司")
    private Integer contentType;

    /**内容ID*/
    @Excel(name = "内容ID", width = 15)
    @ApiModelProperty(value = "内容ID")
    private String contentId;

    /**用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**链接地址*/
    @Excel(name = "链接地址", width = 15)
    @ApiModelProperty(value = "链接地址")
    private String link;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
