package org.jeecg.modules.wh.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 用户门户记录统计VO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WhRecordStatisticsVO", description = "用户门户记录统计VO")
public class WhRecordStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID")
    private String pid;

    /**
     * 历史浏览记录数量
     */
    @ApiModelProperty(value = "历史浏览记录数量")
    private Integer browseCount;

    /**
     * 收藏记录数量
     */
    @ApiModelProperty(value = "收藏记录数量")
    private Integer favoriteCount;

    /**
     * 点赞记录数量
     */
    @ApiModelProperty(value = "点赞记录数量")
    private Integer likeCount;

    /**
     * 分享记录数量
     */
    @ApiModelProperty(value = "分享记录数量")
    private Integer shareCount;
}
