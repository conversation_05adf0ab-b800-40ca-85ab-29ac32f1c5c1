package org.jeecg.modules.wh.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户门户记录统计DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WhRecordStatisticsDTO", description = "用户门户记录统计DTO")
public class WhRecordStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID", required = true)
    @NotBlank(message = "内容ID不能为空")
    private String pid;
    /**
     */
    @ApiModelProperty(value = "内容类型：1.保险产品 2.保险资讯 3.保险公司")
    private Integer contentType;

    /**
     * 记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录
     */
    @ApiModelProperty(value = "记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录", required = true)
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;


    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    private String link;
}
