package org.jeecg.modules.wh.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.wh.entity.WhRecordStatistics;
import org.jeecg.modules.wh.mapper.WhRecordStatisticsMapper;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.jeecg.modules.wh.dto.WhRecordStatisticsDTO;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 用户门户记录统计表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class WhRecordStatisticsServiceImpl extends ServiceImpl<WhRecordStatisticsMapper, WhRecordStatistics> implements IWhRecordStatisticsService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementCount(WhRecordStatisticsDTO dto) {
        try {
            // 查询是否已存在记录
            LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WhRecordStatistics::getPid, dto.getPid())
                       .eq(WhRecordStatistics::getRecordType, dto.getRecordType());

            WhRecordStatistics existRecord = this.getOne(queryWrapper);

            if (existRecord != null) {
                // 存在则数量+1
                existRecord.setCount(existRecord.getCount() + 1);
                this.updateById(existRecord);
                log.info("更新统计记录成功，内容ID：{}，记录类型：{}，当前数量：{}",
                    dto.getPid(), dto.getRecordType(), existRecord.getCount());
            } else {
                // 不存在则创建新记录，数量为1
                WhRecordStatistics newRecord = new WhRecordStatistics();
                newRecord.setPid(dto.getPid());
                newRecord.setRecordType(dto.getRecordType());
                newRecord.setCount(1);
                this.save(newRecord);
                log.info("创建统计记录成功，内容ID：{}，记录类型：{}，数量：1",
                    dto.getPid(), dto.getRecordType());
            }
            return true;
        } catch (Exception e) {
            log.error("增加统计数量失败，参数：{}", dto, e);
            throw new RuntimeException("增加统计数量失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean decrementCount(WhRecordStatisticsDTO dto) {
        try {
            // 查询是否已存在记录
            LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WhRecordStatistics::getPid, dto.getPid())
                       .eq(WhRecordStatistics::getRecordType, dto.getRecordType());

            WhRecordStatistics existRecord = this.getOne(queryWrapper);

            if (existRecord != null && existRecord.getCount() > 0) {
                // 存在且数量大于0则数量-1
                existRecord.setCount(existRecord.getCount() - 1);
                this.updateById(existRecord);
                log.info("减少统计记录成功，内容ID：{}，记录类型：{}，当前数量：{}",
                    dto.getPid(), dto.getRecordType(), existRecord.getCount());
                return true;
            } else {
                log.warn("减少统计记录失败，记录不存在或数量已为0，内容ID：{}，记录类型：{}",
                    dto.getPid(), dto.getRecordType());
                return false;
            }
        } catch (Exception e) {
            log.error("减少统计数量失败，参数：{}", dto, e);
            throw new RuntimeException("减少统计数量失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementCountByIp(WhRecordStatisticsDTO dto, String userIp) {
        try {
            // 生成Redis key，格式：ip_action:内容ID:记录类型:日期
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String redisKey = String.format("ip_action:%s:%s:%s", dto.getPid(), dto.getRecordType(), today);

            // 检查该IP今天是否已经操作过
            Boolean hasActioned = redisTemplate.opsForSet().isMember(redisKey, userIp);

            if (hasActioned) {
                // 如果已经操作过，执行取消操作
                // 从Redis中移除IP记录
                redisTemplate.opsForSet().remove(redisKey, userIp);

                // 减少统计数量
                LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WhRecordStatistics::getPid, dto.getPid())
                           .eq(WhRecordStatistics::getRecordType, dto.getRecordType());

                WhRecordStatistics existRecord = this.getOne(queryWrapper);
                if (existRecord != null && existRecord.getCount() > 0) {
                    existRecord.setCount(existRecord.getCount() - 1);
                    this.updateById(existRecord);
                    log.info("IP {} 取消操作成功，内容ID：{}，记录类型：{}，当前数量：{}",
                        userIp, dto.getPid(), dto.getRecordType(), existRecord.getCount());
                    return true;
                } else {
                    log.warn("IP {} 取消操作失败，统计记录不存在或数量已为0", userIp);
                    return false;
                }
            } else {
                // 如果没有操作过，执行添加操作
                // 记录IP操作
                redisTemplate.opsForSet().add(redisKey, userIp);
                // 设置过期时间为24小时
                redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);

                // 更新统计数量
                LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WhRecordStatistics::getPid, dto.getPid())
                           .eq(WhRecordStatistics::getRecordType, dto.getRecordType());

                WhRecordStatistics existRecord = this.getOne(queryWrapper);

                if (existRecord != null) {
                    // 存在则数量+1
                    existRecord.setCount(existRecord.getCount() + 1);
                    this.updateById(existRecord);
                    log.info("IP {} 更新统计记录成功，内容ID：{}，记录类型：{}，当前数量：{}",
                        userIp, dto.getPid(), dto.getRecordType(), existRecord.getCount());
                } else {
                    // 不存在则创建新记录，数量为1
                    WhRecordStatistics newRecord = new WhRecordStatistics();
                    newRecord.setPid(dto.getPid());
                    newRecord.setRecordType(dto.getRecordType());
                    newRecord.setCount(1);
                    this.save(newRecord);
                    log.info("IP {} 创建统计记录成功，内容ID：{}，记录类型：{}，数量：1",
                        userIp, dto.getPid(), dto.getRecordType());
                }
                return true;
            }
        } catch (Exception e) {
            log.error("IP {} 操作统计数量失败，参数：{}", userIp, dto, e);
            throw new RuntimeException("操作统计数量失败", e);
        }
    }

    @Override
    public boolean hasIpActionedToday(WhRecordStatisticsDTO dto, String userIp) {
        try {
            // 生成Redis key，格式：ip_action:内容ID:记录类型:日期
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String redisKey = String.format("ip_action:%s:%s:%s", dto.getPid(), dto.getRecordType(), today);

            // 检查该IP今天是否已经操作过
            Boolean hasActioned = redisTemplate.opsForSet().isMember(redisKey, userIp);
            return hasActioned != null && hasActioned;
        } catch (Exception e) {
            log.error("检查IP {} 操作状态失败，参数：{}", userIp, dto, e);
            return false;
        }
    }

    @Override
    public WhRecordStatisticsVO getStatisticsByPid(String pid) {
        try {
            // 查询该内容ID的所有统计记录
            LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WhRecordStatistics::getPid, pid);
            List<WhRecordStatistics> records = this.list(queryWrapper);

            // 构建返回结果
            WhRecordStatisticsVO vo = new WhRecordStatisticsVO();
            vo.setPid(pid);
            vo.setBrowseCount(0);
            vo.setFavoriteCount(0);
            vo.setLikeCount(0);
            vo.setShareCount(0);

            // 根据记录类型设置对应的数量
            for (WhRecordStatistics record : records) {
                switch (record.getRecordType()) {
                    case 2: // 收藏记录
                        vo.setFavoriteCount(record.getCount());
                        break;
                    case 3: // 点赞记录
                        vo.setLikeCount(record.getCount());
                        break;
                    default:
                        break;
                }
            }

            return vo;
        } catch (Exception e) {
            log.error("查询统计信息失败，内容ID：{}", pid, e);
            throw new RuntimeException("查询统计信息失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatistics(String pid, Integer likeCount, Integer favoriteCount) {
        try {
            // 更新点赞数
            if (likeCount != null) {
                updateOrCreateRecord(pid, 3, likeCount);
            }

            // 更新收藏数
            if (favoriteCount != null) {
                updateOrCreateRecord(pid, 2, favoriteCount);
            }

            log.info("更新统计数据成功，内容ID：{}，点赞数：{}，收藏数：{}", pid, likeCount, favoriteCount);
            return true;
        } catch (Exception e) {
            log.error("更新统计数据失败，内容ID：{}，点赞数：{}，收藏数：{}", pid, likeCount, favoriteCount, e);
            throw new RuntimeException("更新统计数据失败", e);
        }
    }

    /**
     * 更新或创建统计记录
     * @param pid 内容ID
     * @param recordType 记录类型
     * @param count 数量
     */
    private void updateOrCreateRecord(String pid, Integer recordType, Integer count) {
        LambdaQueryWrapper<WhRecordStatistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WhRecordStatistics::getPid, pid)
                   .eq(WhRecordStatistics::getRecordType, recordType);

        WhRecordStatistics existRecord = this.getOne(queryWrapper);

        if (existRecord != null) {
            // 存在则更新数量
            existRecord.setCount(count);
            this.updateById(existRecord);
        } else {
            // 不存在则创建新记录
            WhRecordStatistics newRecord = new WhRecordStatistics();
            newRecord.setPid(pid);
            newRecord.setRecordType(recordType);
            newRecord.setCount(count);
            this.save(newRecord);
        }
    }
}
