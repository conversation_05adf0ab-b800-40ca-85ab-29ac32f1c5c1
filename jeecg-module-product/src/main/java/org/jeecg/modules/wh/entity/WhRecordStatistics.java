package org.jeecg.modules.wh.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: 用户门户记录统计表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Data
@TableName("wh_record_statistics")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_record_statistics对象", description="用户门户记录统计表")
public class WhRecordStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**数量*/
    @Excel(name = "数量", width = 15)
    @ApiModelProperty(value = "数量")
    private Integer count;
    
    /**记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录*/
    @Excel(name = "记录类型", width = 15, dicCode = "record_type")
    @ApiModelProperty(value = "记录类型：1.历史浏览记录 2.收藏记录 3.点赞记录 4.分享记录")
    private Integer recordType;
    
    /**对应的内容ID*/
    @Excel(name = "对应的内容ID", width = 15)
    @ApiModelProperty(value = "对应的内容ID")
    private String pid;
}
