package org.jeecg.modules.wh.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 用户门户记录统计更新VO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WhRecordStatisticsUpdateVO", description = "用户门户记录统计更新VO")
public class WhRecordStatisticsUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID", required = true)
    @NotBlank(message = "内容ID不能为空")
    private String pid;

    /**
     * 点赞数
     */
    @ApiModelProperty(value = "点赞数s")
    private Integer likeCount;

    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Integer favoriteCount;
}
