package org.jeecg.modules.wh.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户门户记录VO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WhRecordVO", description = "用户门户记录VO")
public class WhRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ApiModelProperty(value = "记录ID")
    private Long id;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID")
    private String contentId;

    /**
     * 内容类型：1.保险产品 2.保险资讯 3.保险公司
     */
    @ApiModelProperty(value = "内容类型：1.保险产品 2.保险资讯 3.保险公司")
    private Integer contentType;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    private String link;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
