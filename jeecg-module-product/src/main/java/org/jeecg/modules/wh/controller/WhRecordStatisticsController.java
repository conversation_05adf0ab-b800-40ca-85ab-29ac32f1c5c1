package org.jeecg.modules.wh.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.IpUtils;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordStatisticsDTO;
import org.jeecg.modules.wh.dto.WhRecordSaveDTO;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;
import org.jeecg.modules.wh.vo.WhRecordStatisticsUpdateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Description: 用户门户记录统计表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="用户门户记录统计管理")
@RestController
@RequestMapping("/wh/recordStatistics")
@Slf4j
public class WhRecordStatisticsController {

    @Autowired
    private IWhRecordStatisticsService whRecordStatisticsService;

    @Autowired
    private IWhRecordService whRecordService;

    /**
     * 增加记录统计数量
     *
     * @param dto 统计参数
     * @return
     */
    @ApiOperation(value="切换记录统计数量", notes="点赞/收藏的切换操作，可以增加或减少数量")
    @PostMapping(value = "/increment")
    public Result<String> incrementCount(@Valid @RequestBody WhRecordStatisticsDTO dto, HttpServletRequest request) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userIp = IpUtils.getIpAddr(request);

            if (sysUser == null) {
                // 未登录用户，按照IP来限制，一天一个IP只能点击一次，并且不能取消点击
                dto.setUserId(userIp); // 使用IP作为用户标识
                boolean success = whRecordStatisticsService.incrementCountByIp(dto, userIp);
                if (success) {
                    return Result.OK("操作成功");
                } else {
                    return Result.error("今日已操作过，请明天再试");
                }
            } else {
                // 已登录用户，可以点赞和收藏的切换操作
                dto.setUserId(sysUser.getId());

                if (dto.getRecordType() == 2) {
                    // 收藏操作：检查是否已收藏
                    boolean exists = whRecordService.existsUserRecord(
                        sysUser.getId(), dto.getPid(), dto.getContentType(), 2);

                    if (exists) {
                        // 已收藏，执行取消收藏操作
                        whRecordService.deleteUserRecord(
                            sysUser.getId(), dto.getPid(), dto.getContentType(), 2);
                        whRecordStatisticsService.decrementCount(dto);
                        return Result.OK("取消收藏成功");
                    } else {
                        // 未收藏，执行收藏操作
                        WhRecordSaveDTO saveDTO = new WhRecordSaveDTO();
                        saveDTO.setUserId(sysUser.getId());
                        saveDTO.setContentId(dto.getPid());
                        saveDTO.setContentType(dto.getContentType());
                        saveDTO.setRecordType(2);
                        saveDTO.setLink(dto.getLink()); // 保存链接信息
                        whRecordService.saveUserRecord(saveDTO);
                        whRecordStatisticsService.incrementCount(dto);
                        return Result.OK("收藏成功");
                    }
                } else if (dto.getRecordType() == 3) {
                    // 点赞操作：检查是否已点赞
                    boolean exists = whRecordService.existsUserRecord(
                        sysUser.getId(), dto.getPid(), dto.getContentType(), 3);

                    if (exists) {
                        // 已点赞，执行取消点赞操作
                        whRecordService.deleteUserRecord(
                            sysUser.getId(), dto.getPid(), dto.getContentType(), 3);
                        whRecordStatisticsService.decrementCount(dto);
                        return Result.OK("取消点赞成功");
                    } else {
                        // 未点赞，执行点赞操作
                        WhRecordSaveDTO saveDTO = new WhRecordSaveDTO();
                        saveDTO.setUserId(sysUser.getId());
                        saveDTO.setContentId(dto.getPid());
                        saveDTO.setContentType(dto.getContentType());
                        saveDTO.setRecordType(3);
                        saveDTO.setLink(dto.getLink()); // 保存链接信息
                        whRecordService.saveUserRecord(saveDTO);
                        whRecordStatisticsService.incrementCount(dto);
                        return Result.OK("点赞成功");
                    }
                } else {
                    // 其他操作类型，直接增加统计
                    boolean success = whRecordStatisticsService.incrementCount(dto);
                    if (success) {
                        return Result.OK("操作成功");
                    } else {
                        return Result.error("操作失败");
                    }
                }
            }
        } catch (Exception e) {
            log.error("操作统计数量失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 增加记录统计数量
     *
     * @param dto 统计参数
     * @return
     */
    @ApiOperation(value="切换记录统计数量", notes="点赞/收藏的切换操作，可以增加或减少数量")
    @PostMapping(value = "/ipIncrementCount")
    public Result<String> ipIncrementCount(@Valid @RequestBody WhRecordStatisticsDTO dto, HttpServletRequest request) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userIp = IpUtils.getIpAddr(request);

            if (sysUser == null) {
                // 未登录用户，支持点赞/取消点赞的切换操作
                dto.setUserId(userIp); // 使用IP作为用户标识

                // 检查是否已经操作过
                boolean hasActioned = whRecordStatisticsService.hasIpActionedToday(dto, userIp);
                boolean success = whRecordStatisticsService.incrementCountByIp(dto, userIp);

                if (success) {
                    if (hasActioned) {
                        // 之前已操作过，现在是取消操作
                        if (dto.getRecordType() == 3) {
                            return Result.OK("取消点赞成功");
                        } else if (dto.getRecordType() == 2) {
                            return Result.OK("取消收藏成功");
                        } else {
                            return Result.OK("取消操作成功");
                        }
                    } else {
                        // 之前未操作过，现在是添加操作
                        if (dto.getRecordType() == 3) {
                            return Result.OK("点赞成功");
                        } else if (dto.getRecordType() == 2) {
                            return Result.OK("收藏成功");
                        } else {
                            return Result.OK("操作成功");
                        }
                    }
                } else {
                    return Result.error("操作失败");
                }
            } else {
                // 已登录用户，可以点赞和收藏的切换操作
                dto.setUserId(sysUser.getId());

                if (dto.getRecordType() == 2) {
                    // 收藏操作：检查是否已收藏
                    boolean exists = whRecordService.existsUserRecord(
                            sysUser.getId(), dto.getPid(), dto.getContentType(), 2);

                    if (exists) {
                        // 已收藏，执行取消收藏操作
                        whRecordService.deleteUserRecord(
                                sysUser.getId(), dto.getPid(), dto.getContentType(), 2);
                        whRecordStatisticsService.decrementCount(dto);
                        return Result.OK("取消收藏成功");
                    } else {
                        // 未收藏，执行收藏操作
                        WhRecordSaveDTO saveDTO = new WhRecordSaveDTO();
                        saveDTO.setUserId(sysUser.getId());
                        saveDTO.setContentId(dto.getPid());
                        saveDTO.setContentType(dto.getContentType());
                        saveDTO.setRecordType(2);
                        saveDTO.setLink(dto.getLink()); // 保存链接信息
                        whRecordService.saveUserRecord(saveDTO);
                        whRecordStatisticsService.incrementCount(dto);
                        return Result.OK("收藏成功");
                    }
                } else if (dto.getRecordType() == 3) {
                    // 点赞操作：检查是否已点赞
                    boolean exists = whRecordService.existsUserRecord(
                            sysUser.getId(), dto.getPid(), dto.getContentType(), 3);

                    if (exists) {
                        // 已点赞，执行取消点赞操作
                        whRecordService.deleteUserRecord(
                                sysUser.getId(), dto.getPid(), dto.getContentType(), 3);
                        whRecordStatisticsService.decrementCount(dto);
                        return Result.OK("取消点赞成功");
                    } else {
                        // 未点赞，执行点赞操作
                        WhRecordSaveDTO saveDTO = new WhRecordSaveDTO();
                        saveDTO.setUserId(sysUser.getId());
                        saveDTO.setContentId(dto.getPid());
                        saveDTO.setContentType(dto.getContentType());
                        saveDTO.setRecordType(3);
                        saveDTO.setLink(dto.getLink()); // 保存链接信息
                        whRecordService.saveUserRecord(saveDTO);
                        whRecordStatisticsService.incrementCount(dto);
                        return Result.OK("点赞成功");
                    }
                } else {
                    // 其他操作类型，直接增加统计
                    boolean success = whRecordStatisticsService.incrementCount(dto);
                    if (success) {
                        return Result.OK("操作成功");
                    } else {
                        return Result.error("操作失败");
                    }
                }
            }
        } catch (Exception e) {
            log.error("操作统计数量失败", e);
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据内容ID获取统计信息
     *
     * @param pid 内容ID
     * @return
     */
    @ApiOperation(value="获取统计信息", notes="根据内容ID查询4个记录类型的统计数量")
    @GetMapping(value = "/getStatistics")
    public Result<WhRecordStatisticsVO> getStatisticsByPid(@RequestParam(name="pid", required=true) String pid) {
        try {
            WhRecordStatisticsVO vo = whRecordStatisticsService.getStatisticsByPid(pid);
            return Result.OK(vo);
        } catch (Exception e) {
            log.error("查询统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 直接更新统计数量（用于管理后台编辑）
     *
     * @param updateVO 更新参数
     * @return
     */
    @ApiOperation(value="更新统计数量", notes="直接更新点赞数和收藏数，用于管理后台编辑")
    @PostMapping(value = "/updateStatistics")
    public Result<String> updateStatistics(@Valid @RequestBody WhRecordStatisticsUpdateVO updateVO) {
        try {
//            boolean success = whRecordStatisticsService.updateStatistics(
//                updateVO.getPid(),
//                updateVO.getLikeCount(),
//                updateVO.getFavoriteCount()
//            );
//            if (success) {
//
//            } else {
//                return Result.error("更新失败");
//            }
            return Result.OK("更新成功");
        } catch (Exception e) {
            log.error("更新统计数量失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }
}
