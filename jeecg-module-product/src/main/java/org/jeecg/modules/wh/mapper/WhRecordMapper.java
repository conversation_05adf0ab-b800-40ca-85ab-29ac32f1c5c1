package org.jeecg.modules.wh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;

import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface WhRecordMapper extends BaseMapper<WhRecord> {

    /**
     * 检查记录是否存在
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 记录
     */
    WhRecord findExistRecord(@Param("userId") String userId, @Param("contentId") String contentId,
                            @Param("contentType") Integer contentType, @Param("recordType") Integer recordType);

    /**
     * 根据查询条件获取用户记录列表（带标题信息）
     * @param queryDTO 查询条件
     * @return 记录列表
     */
    List<WhRecordVO> getRecordListWithTitle(@Param("queryDTO") WhRecordQueryDTO queryDTO);

    /**
     * 根据查询条件获取用户记录列表（带标题信息）- 分页
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页记录列表
     */
    IPage<WhRecordVO> getRecordListWithTitlePage(IPage<WhRecordVO> page, @Param("queryDTO") WhRecordQueryDTO queryDTO);
}
