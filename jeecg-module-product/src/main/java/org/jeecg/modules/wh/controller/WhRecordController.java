package org.jeecg.modules.wh.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.wh.service.IWhRecordService;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.dto.WhRecordSaveDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="用户门户记录管理")
@RestController
@RequestMapping("/wh/record")
@Slf4j
public class WhRecordController {

    @Autowired
    private IWhRecordService whRecordService;

    /**
     * 保存用户记录
     *
     * @param saveDTO 保存参数
     * @return
     */
    @ApiOperation(value="保存用户记录", notes="保存用户记录")
    @PostMapping(value = "/saveRecord")
    public Result<String> saveRecord(@RequestBody WhRecordSaveDTO saveDTO) {
        try {
            //获取登录用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if(sysUser!=null){
                saveDTO.setUserId(sysUser.getId());
            }else {
                return null;
            }
            boolean success = whRecordService.saveUserRecord(saveDTO);
            if (success) {
                return Result.OK("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存用户记录失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询用户记录列表（带标题信息）- 分页
     *
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param queryDTO 查询条件
     * @return
     */
    @ApiOperation(value="根据条件查询用户记录列表", notes="根据条件查询用户记录列表（带标题信息）- 分页")
    @PostMapping(value = "/getRecordListWithTitle")
    public Result<IPage<WhRecordVO>> getRecordListWithTitle(
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            @RequestBody WhRecordQueryDTO queryDTO) {
        try {
            //获取登录用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if(sysUser != null){
                queryDTO.setUserId(sysUser.getId());
            }

            Page<WhRecordVO> page = new Page<>(pageNo, pageSize);
            IPage<WhRecordVO> pageList = whRecordService.getRecordListWithTitle(page, queryDTO);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("查询用户记录列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
