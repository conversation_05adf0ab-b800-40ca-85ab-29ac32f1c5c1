package org.jeecg.modules.consum.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.consum.dto.RechargeRecodeDto;
import org.jeecg.modules.consum.dto.RechargeSummaryDto;
import org.jeecg.modules.consum.entity.RechargeRecode;
import org.jeecg.modules.consum.service.IRechargeRecodeService;
import org.jeecg.modules.consum.vo.RechargeRecodeExportVo;
import org.jeecg.modules.consum.vo.RechargeRecodeVo;
import org.jeecg.modules.consum.vo.RechargeSummaryVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.consum.vo.RechargeRecodeVo;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Api(tags="模拟充值记录")
@RestController
@RequestMapping("/consum/rechargeRecode")
@Slf4j
public class RechargeRecodeController extends JeecgController<RechargeRecode, IRechargeRecodeService> {
	@Autowired
	private IRechargeRecodeService rechargeRecodeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	//@AutoLog(value = "模拟充值记录-分页列表查询")
	@ApiOperation(value="模拟充值记录-分页列表查询", notes="模拟充值记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RechargeRecodeVo>> queryPageList(RechargeRecodeDto dto,
														 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		Page<RechargeRecodeVo> page = new Page<RechargeRecodeVo>(pageNo, pageSize);
		IPage<RechargeRecodeVo> pageList = rechargeRecodeService.pageList(page, dto);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rechargeRecode
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-添加")
	@ApiOperation(value="模拟充值记录-添加", notes="模拟充值记录-添加")
	@RequiresPermissions("consum:recharge_recode:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RechargeRecode rechargeRecode) {
		rechargeRecodeService.save(rechargeRecode);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rechargeRecode
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-编辑")
	@ApiOperation(value="模拟充值记录-编辑", notes="模拟充值记录-编辑")
	@RequiresPermissions("consum:recharge_recode:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RechargeRecode rechargeRecode) {
		rechargeRecodeService.updateById(rechargeRecode);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-通过id删除")
	@ApiOperation(value="模拟充值记录-通过id删除", notes="模拟充值记录-通过id删除")
	@RequiresPermissions("consum:recharge_recode:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rechargeRecodeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-批量删除")
	@ApiOperation(value="模拟充值记录-批量删除", notes="模拟充值记录-批量删除")
	@RequiresPermissions("consum:recharge_recode:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rechargeRecodeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "模拟充值记录-通过id查询")
	@ApiOperation(value="模拟充值记录-通过id查询", notes="模拟充值记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RechargeRecode> queryById(@RequestParam(name="id",required=true) String id) {
		RechargeRecode rechargeRecode = rechargeRecodeService.getById(id);
		if(rechargeRecode==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rechargeRecode);
	}

	/**
	 * 获取充值汇总信息
	 *
	 * @param dto
	 * @return
	 */
	@ApiOperation(value="充值汇总信息查询", notes="充值汇总信息查询")
	@PostMapping(value = "/summary")
	public Result<RechargeSummaryVo> getRechargeSummary(@RequestBody RechargeSummaryDto dto) {
		RechargeSummaryVo summaryVo = rechargeRecodeService.getRechargeSummary(dto);
		return Result.OK(summaryVo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dto
    */
    @RequiresPermissions("consum:recharge_recode:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, @RequestBody RechargeRecodeDto dto) {
        // 获取导出字段列表（只导出前端展示的字段）
        String exportFields = "orderDate,orderNo,amount,customerPayment,tenantName";

        // 获取选中的记录IDs
        String selections = dto.getSelections();

        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 如果有选中记录，则导出选中的记录
        if (oConvertUtils.isNotEmpty(selections)) {
            // 使用选中的IDs进行查询
            List<String> idList = Arrays.asList(selections.split(","));

            // 获取选中记录的完整数据
            List<RechargeRecodeVo> selectedList = rechargeRecodeService.getListByIds(idList);

            // 转换为导出VO
            List<RechargeRecodeExportVo> exportList = selectedList.stream().map(vo -> {
                RechargeRecodeExportVo exportVo = new RechargeRecodeExportVo();
                exportVo.setOrderDate(vo.getOrderDate());
                exportVo.setOrderNo(vo.getOrderNo());
                exportVo.setAmount(vo.getAmount());
                exportVo.setCustomerPayment("已收款");
                exportVo.setTenantName(vo.getTenantName());
                return exportVo;
            }).collect(Collectors.toList());

            // 导出Excel
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "充值记录");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, RechargeRecodeExportVo.class);
            ExportParams exportParams = new ExportParams("充值记录", "导出人:" + sysUser.getRealname(), "充值记录");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
            return mv;
        } else {
            // 如果没有选中记录，则按照查询条件导出
            try {
                // 使用分页查询，确保导出的数据与列表一致
                Page<RechargeRecodeVo> page = new Page<>(1, 999999999); // 设置合适的分页大小
                IPage<RechargeRecodeVo> pageList = getExportData(dto, page);
                List<RechargeRecodeVo> voList = pageList.getRecords();

                // 转换为导出VO
                List<RechargeRecodeExportVo> exportList = voList.stream().map(vo -> {
                    RechargeRecodeExportVo exportVo = new RechargeRecodeExportVo();
                    exportVo.setOrderDate(vo.getOrderDate());
                    exportVo.setOrderNo(vo.getOrderNo());
                    exportVo.setAmount(vo.getAmount());
                    exportVo.setCustomerPayment("已收款");
                    exportVo.setTenantName(vo.getTenantName());
                    return exportVo;
                }).collect(Collectors.toList());

                // 导出Excel
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "充值记录");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, RechargeRecodeExportVo.class);
                ExportParams exportParams = new ExportParams("充值记录", "导出人:" + sysUser.getRealname(), "充值记录");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
                return mv;
            } catch (Exception e) {
                log.error("导出Excel异常", e);
                throw new RuntimeException("导出异常,请联系管理员");
            }
        }
    }

    /**
     * 获取导出数据
     *
     * @param dto 查询条件
     * @param page 分页参数
     * @return 查询结果
     */
    private IPage<RechargeRecodeVo> getExportData(RechargeRecodeDto dto, Page<RechargeRecodeVo> page) {
        return rechargeRecodeService.pageList(page, dto);
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("consum:recharge_recode:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RechargeRecode.class);
    }

}
