<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.consum.mapper.RechargeRecodeDetMapper">

    <select id="pageList" resultType="org.jeecg.modules.consum.vo.RechargeRecodeDetVo">
        SELECT
            rrd.id,
            rrd.create_by AS createBy,
            rrd.order_date AS orderDate,
            rrd.update_by AS updateBy,
            rrd.update_time AS updateTime,
            rrd.click_num AS clickNum,
            rrd.amount,
            rrd.price,
            rrd.tenant_id AS tenantId,
            st.name AS tenantName
        FROM recharge_recode_det rrd
        LEFT JOIN sys_tenant st ON st.id = rrd.tenant_id
        <where>
            1 = 1
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND rrd.tenant_id IN
                <foreach collection="dto.tenantIds" item="tenantId" open="(" close=")" separator=",">
                    #{tenantId}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != ''">
                AND rrd.order_date >= #{dto.startDate}
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                AND rrd.order_date &lt;= #{dto.endDate}
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (rrd.click_num LIKE CONCAT('%', #{dto.keyword}, '%')
                     OR st.name LIKE CONCAT('%', #{dto.keyword}, '%'))
            </if>
            <if test="dto.clickNumStart != null and dto.clickNumStart != ''">
                AND CAST(rrd.click_num AS UNSIGNED) >= CAST(#{dto.clickNumStart} AS UNSIGNED)
            </if>
            <if test="dto.clickNumEnd != null and dto.clickNumEnd != ''">
                AND CAST(rrd.click_num AS UNSIGNED) &lt;= CAST(#{dto.clickNumEnd} AS UNSIGNED)
            </if>
            <if test="dto.amountStart != null and dto.amountStart != ''">
                AND rrd.amount >= #{dto.amountStart}
            </if>
            <if test="dto.amountEnd != null and dto.amountEnd != ''">
                AND rrd.amount &lt;= #{dto.amountEnd}
            </if>
            <if test="dto.canViewFutureData != null and dto.canViewFutureData == 0">
                AND rrd.order_date &lt;= CURDATE()
            </if>
        </where>
        <if test="dto.orderBy != null and dto.orderBy != ''">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null or dto.orderBy == ''">
            ORDER BY rrd.order_date DESC
        </if>
    </select>

    <select id="getSummary" resultType="org.jeecg.modules.consum.vo.RechargeRecodeDetSummaryVo">
        SELECT
            SUM(CAST(rrd.click_num AS UNSIGNED)) AS totalClickNum,
            SUM(rrd.amount) AS totalAmount
        FROM recharge_recode_det rrd
        LEFT JOIN sys_tenant st ON st.id = rrd.tenant_id
        <where>
            1 = 1
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND rrd.tenant_id IN
                <foreach collection="dto.tenantIds" item="tenantId" open="(" close=")" separator=",">
                    #{tenantId}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != ''">
                AND rrd.order_date >= #{dto.startDate}
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                AND rrd.order_date &lt;= #{dto.endDate}
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (rrd.click_num LIKE CONCAT('%', #{dto.keyword}, '%')
                     OR st.name LIKE CONCAT('%', #{dto.keyword}, '%'))
            </if>
            <if test="dto.clickNumStart != null and dto.clickNumStart != ''">
                AND CAST(rrd.click_num AS UNSIGNED) >= CAST(#{dto.clickNumStart} AS UNSIGNED)
            </if>
            <if test="dto.clickNumEnd != null and dto.clickNumEnd != ''">
                AND CAST(rrd.click_num AS UNSIGNED) &lt;= CAST(#{dto.clickNumEnd} AS UNSIGNED)
            </if>
            <if test="dto.amountStart != null and dto.amountStart != ''">
                AND rrd.amount >= #{dto.amountStart}
            </if>
            <if test="dto.amountEnd != null and dto.amountEnd != ''">
                AND rrd.amount &lt;= #{dto.amountEnd}
            </if>
            <if test="dto.canViewFutureData != null and dto.canViewFutureData == 0">
                AND rrd.order_date &lt;= CURDATE()
            </if>
        </where>
    </select>

    <select id="getListByIds" resultType="org.jeecg.modules.consum.vo.RechargeRecodeDetVo">
        SELECT
            rrd.id,
            rrd.order_date AS orderDate,
            rrd.click_num AS clickNum,
            rrd.amount,
            rrd.price,
            rrd.tenant_id AS tenantId,
            st.name AS tenantName
        FROM recharge_recode_det rrd
        LEFT JOIN sys_tenant st ON st.id = rrd.tenant_id
        WHERE rrd.id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY rrd.order_date DESC
    </select>

    <select id="getCurrentMonthConsumeAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM recharge_recode_det
        <where>
            1 = 1
            AND DATE_FORMAT(order_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
            <if test="dto.tenantId != null and dto.tenantId != '' and dto.tenantId != '0'">
                AND tenant_id = #{dto.tenantId}
            </if>
        </where>
    </select>

</mapper>