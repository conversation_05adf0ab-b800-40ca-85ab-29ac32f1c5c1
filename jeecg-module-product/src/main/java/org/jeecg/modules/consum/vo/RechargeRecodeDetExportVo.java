package org.jeecg.modules.consum.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 测试环境用消耗明细导出VO
 * @Author: jeecg-boot
 * @Date: 2025-08-30
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class RechargeRecodeDetExportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "日期")
    private Date orderDate;

    @Excel(name = "点击数", width = 15)
    @ApiModelProperty(value = "点击数")
    private String clickNum;

    @Excel(name = "消耗金额", width = 15)
    @ApiModelProperty(value = "消耗金额")
    private BigDecimal amount;

    @Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @Excel(name = "租户名称", width = 20)
    @ApiModelProperty(value = "租户名称")
    private String tenantName;
}
