package org.jeecg.modules.consum.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.config.TenantContext;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.mybatis.TimeRestrictionUtil;
import org.jeecg.modules.consum.dto.RechargeRecodeDetDto;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import org.jeecg.modules.consum.mapper.RechargeRecodeDetMapper;
import org.jeecg.modules.consum.service.IRechargeRecodeDetService;
import org.jeecg.modules.consum.vo.RechargeRecodeDetVo;
import org.jeecg.modules.consum.vo.RechargeRecodeDetSummaryVo;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Service
public class RechargeRecodeDetServiceImpl extends ServiceImpl<RechargeRecodeDetMapper, RechargeRecodeDet> implements IRechargeRecodeDetService {

    @Autowired
    private TimeRestrictionUtil timeRestrictionUtil;

    @Override
    public IPage<RechargeRecodeDetVo> pageList(Page<RechargeRecodeDetVo> page, RechargeRecodeDetDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }

        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantContext.getTenant());
        dto.setCanViewFutureData(canViewFutureData);

        // 处理时间范围，确保不超过当前时间
        dto.setStartDate(timeRestrictionUtil.processStartDate(dto.getStartDate(), canViewFutureData));
        dto.setEndDate(timeRestrictionUtil.processEndDate(dto.getEndDate(), canViewFutureData));

        return this.baseMapper.pageList(page, dto);
    }

    @Override
    public RechargeRecodeDetSummaryVo getSummary(RechargeRecodeDetDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }

        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }

        // 处理租户ID并设置时间限制标识
        Integer tenantId = 0; // 默认查询所有租户
        if (dto.getTenantId() != null && !dto.getTenantId().trim().isEmpty() && !"0".equals(dto.getTenantId().trim())) {
            tenantId = Integer.parseInt(dto.getTenantId().trim());
        }

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(dto.getTenantId());
        dto.setCanViewFutureData(canViewFutureData);

        // 处理时间范围，确保不超过当前时间
        dto.setStartDate(timeRestrictionUtil.processStartDate(dto.getStartDate(), canViewFutureData));
        dto.setEndDate(timeRestrictionUtil.processEndDate(dto.getEndDate(), canViewFutureData));

        return this.baseMapper.getSummary(dto);
    }

    @Override
    public List<RechargeRecodeDetVo> getListByIds(List<String> idList) {
        return this.baseMapper.getListByIds(idList);
    }
}
