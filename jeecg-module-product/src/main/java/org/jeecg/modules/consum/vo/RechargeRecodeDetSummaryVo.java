package org.jeecg.modules.consum.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class RechargeRecodeDetSummaryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总点击数")
    private Long totalClickNum;

    @ApiModelProperty(value = "总消耗金额")
    private BigDecimal totalAmount;
}
