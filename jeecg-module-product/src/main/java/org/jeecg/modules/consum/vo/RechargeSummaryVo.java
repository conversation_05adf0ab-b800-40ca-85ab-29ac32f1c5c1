package org.jeecg.modules.consum.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 充值汇总VO
 * @Author: jeecg-boot
 * @Date: 2025-08-30
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class RechargeSummaryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总充值金额")
    private BigDecimal totalRechargeAmount;

    @ApiModelProperty(value = "本月充值金额")
    private BigDecimal currentMonthRechargeAmount;

    @ApiModelProperty(value = "本月消耗金额")
    private BigDecimal currentMonthConsumeAmount;

    @ApiModelProperty(value = "今日点击数")
    private Long todayClickCount;
}
