package org.jeecg.modules.consum.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.consum.vo.RechargeRecodeDetExportVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.consum.dto.RechargeRecodeDetDto;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import org.jeecg.modules.consum.service.IRechargeRecodeDetService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.consum.vo.RechargeRecodeDetVo;
import org.jeecg.modules.consum.vo.RechargeRecodeDetSummaryVo;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Api(tags="测试环境用消耗明细")
@RestController
@RequestMapping("/consum/rechargeRecodeDet")
@Slf4j
public class RechargeRecodeDetController extends JeecgController<RechargeRecodeDet, IRechargeRecodeDetService> {
	@Autowired
	private IRechargeRecodeDetService rechargeRecodeDetService;
	
	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="测试环境用消耗明细-分页列表查询", notes="测试环境用消耗明细-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RechargeRecodeDetVo>> queryPageList(RechargeRecodeDetDto dto,
														  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														  HttpServletRequest req) {
		Page<RechargeRecodeDetVo> page = new Page<RechargeRecodeDetVo>(pageNo, pageSize);
		IPage<RechargeRecodeDetVo> pageList = rechargeRecodeDetService.pageList(page, dto);
		return Result.OK(pageList);
	}

	/**
	 * 合计查询
	 *
	 * @param dto
	 * @return
	 */
	@ApiOperation(value="测试环境用消耗明细-合计查询", notes="测试环境用消耗明细-合计查询")
	@GetMapping(value = "/summary")
	public Result<RechargeRecodeDetSummaryVo> getSummary(RechargeRecodeDetDto dto) {
		RechargeRecodeDetSummaryVo summary = rechargeRecodeDetService.getSummary(dto);
		return Result.OK(summary);
	}

	/**
	 *   添加
	 *
	 * @param rechargeRecodeDet
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-添加")
	@ApiOperation(value="测试环境用消耗明细-添加", notes="测试环境用消耗明细-添加")
	@RequiresPermissions("consum:recharge_recode_det:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RechargeRecodeDet rechargeRecodeDet) {
		rechargeRecodeDetService.save(rechargeRecodeDet);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rechargeRecodeDet
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-编辑")
	@ApiOperation(value="测试环境用消耗明细-编辑", notes="测试环境用消耗明细-编辑")
	@RequiresPermissions("consum:recharge_recode_det:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RechargeRecodeDet rechargeRecodeDet) {
		rechargeRecodeDetService.updateById(rechargeRecodeDet);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-通过id删除")
	@ApiOperation(value="测试环境用消耗明细-通过id删除", notes="测试环境用消耗明细-通过id删除")
	@RequiresPermissions("consum:recharge_recode_det:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rechargeRecodeDetService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-批量删除")
	@ApiOperation(value="测试环境用消耗明细-批量删除", notes="测试环境用消耗明细-批量删除")
	@RequiresPermissions("consum:recharge_recode_det:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rechargeRecodeDetService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "测试环境用消耗明细-通过id查询")
	@ApiOperation(value="测试环境用消耗明细-通过id查询", notes="测试环境用消耗明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RechargeRecodeDet> queryById(@RequestParam(name="id",required=true) String id) {
		RechargeRecodeDet rechargeRecodeDet = rechargeRecodeDetService.getById(id);
		if(rechargeRecodeDet==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rechargeRecodeDet);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dto
    */
    @RequiresPermissions("consum:recharge_recode_det:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, @RequestBody RechargeRecodeDetDto dto) {
        // 获取导出字段列表（只导出前端展示的字段）
        String exportFields = "orderDate,clickNum,amount,price,tenantName";

        // 获取选中的记录IDs
        String selections = dto.getSelections();

        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 如果有选中记录，则导出选中的记录
        if (oConvertUtils.isNotEmpty(selections)) {
            // 使用选中的IDs进行查询
            List<String> idList = Arrays.asList(selections.split(","));

            // 获取选中记录的完整数据
            List<RechargeRecodeDetVo> selectedList = rechargeRecodeDetService.getListByIds(idList);

            // 转换为导出VO
            List<RechargeRecodeDetExportVo> exportList = selectedList.stream().map(vo -> {
                RechargeRecodeDetExportVo exportVo = new RechargeRecodeDetExportVo();
                exportVo.setOrderDate(vo.getOrderDate());
                exportVo.setClickNum(vo.getClickNum());
                exportVo.setAmount(vo.getAmount());
                exportVo.setPrice(vo.getPrice());
                exportVo.setTenantName(vo.getTenantName());
                return exportVo;
            }).collect(Collectors.toList());

            // 导出Excel
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "日消耗明细");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, RechargeRecodeDetExportVo.class);
            ExportParams exportParams = new ExportParams("日消耗明细", "导出人:" + sysUser.getRealname(), "日消耗明细");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
            return mv;
        } else {
            // 如果没有选中记录，则按照查询条件导出
            try {
                // 使用分页查询，确保导出的数据与列表一致
                Page<RechargeRecodeDetVo> page = new Page<>(1, 999999999); // 设置合适的分页大小
                IPage<RechargeRecodeDetVo> pageList = getExportData(dto, page);
                List<RechargeRecodeDetVo> voList = pageList.getRecords();

                // 转换为导出VO
                List<RechargeRecodeDetExportVo> exportList = voList.stream().map(vo -> {
                    RechargeRecodeDetExportVo exportVo = new RechargeRecodeDetExportVo();
                    exportVo.setOrderDate(vo.getOrderDate());
                    exportVo.setClickNum(vo.getClickNum());
                    exportVo.setAmount(vo.getAmount());
                    exportVo.setPrice(vo.getPrice());
                    exportVo.setTenantName(vo.getTenantName());
                    return exportVo;
                }).collect(Collectors.toList());

                // 导出Excel
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "日消耗明细");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, RechargeRecodeDetExportVo.class);
                ExportParams exportParams = new ExportParams("日消耗明细", "导出人:" + sysUser.getRealname(), "日消耗明细");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
                return mv;
            } catch (Exception e) {
                log.error("导出Excel异常", e);
                throw new RuntimeException("导出异常,请联系管理员");
            }
        }
    }

    /**
     * 获取导出数据
     *
     * @param dto 查询条件
     * @param page 分页参数
     * @return 查询结果
     */
    private IPage<RechargeRecodeDetVo> getExportData(RechargeRecodeDetDto dto, Page<RechargeRecodeDetVo> page) {
        return rechargeRecodeDetService.pageList(page, dto);
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("consum:recharge_recode_det:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RechargeRecodeDet.class);
    }

}
