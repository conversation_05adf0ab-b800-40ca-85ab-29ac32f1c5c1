<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.consum.mapper.RechargeRecodeMapper">

    <select id="pageList" resultType="org.jeecg.modules.consum.vo.RechargeRecodeVo">
        SELECT
            rr.id,
            rr.create_by AS createBy,
            rr.create_time AS createTime,
            rr.update_by AS updateBy,
            rr.update_time AS updateTime,
            rr.order_no AS orderNo,
            rr.amount,
            rr.order_date AS orderDate,
            rr.tenant_id AS tenantId,
            st.name AS tenantName
        FROM recharge_recode rr
        LEFT JOIN sys_tenant st ON st.id = rr.tenant_id
        <where>
            1 = 1
            <if test="dto.tenantIds != null and dto.tenantIds.size() > 0">
                AND rr.tenant_id IN
                <foreach collection="dto.tenantIds" item="tenantId" open="(" close=")" separator=",">
                    #{tenantId}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != ''">
                AND rr.order_date >= #{dto.startDate}
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                AND rr.order_date &lt;= #{dto.endDate}
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                AND rr.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (rr.order_no LIKE CONCAT('%', #{dto.keyword}, '%')
                     OR st.name LIKE CONCAT('%', #{dto.keyword}, '%'))
            </if>
        </where>
        <if test="dto.orderBy != null and dto.orderBy != ''">
            ORDER BY ${dto.orderBy}
        </if>
        <if test="dto.orderBy == null or dto.orderBy == ''">
            ORDER BY rr.create_time DESC
        </if>
    </select>

    <select id="getListByIds" resultType="org.jeecg.modules.consum.vo.RechargeRecodeVo">
        SELECT
            rr.id,
            rr.order_no AS orderNo,
            rr.amount,
            rr.order_date AS orderDate,
            rr.tenant_id AS tenantId,
            st.name AS tenantName
        FROM recharge_recode rr
        LEFT JOIN sys_tenant st ON st.id = rr.tenant_id
        WHERE rr.id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY rr.order_date DESC
    </select>

    <select id="getTotalRechargeAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM recharge_recode
        <where>
            1 = 1
            <if test="dto.tenantId != null and dto.tenantId != '' and dto.tenantId != '0'">
                AND tenant_id = #{dto.tenantId}
            </if>
        </where>
    </select>

    <select id="getCurrentMonthRechargeAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM recharge_recode
        <where>
            1 = 1
            AND DATE_FORMAT(order_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
            <if test="dto.tenantId != null and dto.tenantId != '' and dto.tenantId != '0'">
                AND tenant_id = #{dto.tenantId}
            </if>
        </where>
    </select>

</mapper>