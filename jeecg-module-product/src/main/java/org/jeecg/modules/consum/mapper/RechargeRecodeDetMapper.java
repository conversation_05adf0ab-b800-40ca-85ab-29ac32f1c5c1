package org.jeecg.modules.consum.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.consum.dto.RechargeRecodeDetDto;
import org.jeecg.modules.consum.dto.RechargeSummaryDto;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.consum.vo.RechargeRecodeDetVo;
import org.jeecg.modules.consum.vo.RechargeRecodeDetSummaryVo;

/**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
public interface RechargeRecodeDetMapper extends BaseMapper<RechargeRecodeDet> {

    /**
     * 分页查询消耗明细
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<RechargeRecodeDetVo> pageList(Page<RechargeRecodeDetVo> page, @Param("dto") RechargeRecodeDetDto dto);

    /**
     * 查询消耗明细合计
     * @param dto 查询条件
     * @return 合计结果
     */
    RechargeRecodeDetSummaryVo getSummary(@Param("dto") RechargeRecodeDetDto dto);

    /**
     * 根据ID列表获取记录
     * @param idList ID列表
     * @return 记录列表
     */
    List<RechargeRecodeDetVo> getListByIds(@Param("idList") List<String> idList);

    /**
     * 查询本月消耗金额
     * @param dto 查询条件
     * @return 本月消耗金额
     */
    BigDecimal getCurrentMonthConsumeAmount(@Param("dto") RechargeSummaryDto dto);
}
