package org.jeecg.modules.consum.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.consum.dto.RechargeRecodeDetDto;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.consum.vo.RechargeRecodeDetVo;
import org.jeecg.modules.consum.vo.RechargeRecodeDetSummaryVo;

import java.util.List;

/**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
public interface IRechargeRecodeDetService extends IService<RechargeRecodeDet> {

    IPage<RechargeRecodeDetVo> pageList(Page<RechargeRecodeDetVo> page, RechargeRecodeDetDto dto);

    /**
     * 查询消耗明细合计
     * @param dto 查询条件
     * @return 合计结果
     */
    RechargeRecodeDetSummaryVo getSummary(RechargeRecodeDetDto dto);

    /**
     * 根据ID列表获取记录
     * @param idList ID列表
     * @return 记录列表
     */
    List<RechargeRecodeDetVo> getListByIds(List<String> idList);
}
