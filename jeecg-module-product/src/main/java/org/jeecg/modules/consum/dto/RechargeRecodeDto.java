package org.jeecg.modules.consum.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class RechargeRecodeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询开始日期", example = "2023-01-01")
    private String startDate;

    @ApiModelProperty(value = "查询结束日期", example = "2023-12-31")
    private String endDate;

    @ApiModelProperty(value = "查询日期")
    private String dateRange;

    @ApiModelProperty(value = "租户id", example = "12345")
    private String tenantId;

    @ApiModelProperty(value = "多租户id集合")
    private List<String> tenantIds;

    @ApiModelProperty(value = "订单号", example = "12345")
    private String orderNo;

    @ApiModelProperty(value = "查询关键字keyword")
    private String keyword;

    @ApiModelProperty(value = "排序")
    private String orderBy;

    @ApiModelProperty(value = "选中ids")
    private String selections;
}
