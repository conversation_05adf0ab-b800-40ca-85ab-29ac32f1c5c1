package org.jeecg.modules.consum.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.consum.dto.RechargeRecodeDto;
import org.jeecg.modules.consum.dto.RechargeSummaryDto;
import org.jeecg.modules.consum.entity.RechargeRecode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.consum.vo.RechargeRecodeVo;

/**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
public interface RechargeRecodeMapper extends BaseMapper<RechargeRecode> {

    /**
     * 分页查询充值记录
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<RechargeRecodeVo> pageList(Page<RechargeRecodeVo> page, @Param("dto") RechargeRecodeDto dto);

    /**
     * 根据ID列表获取记录
     * @param idList ID列表
     * @return 记录列表
     */
    List<RechargeRecodeVo> getListByIds(@Param("idList") List<String> idList);

    /**
     * 查询总充值金额
     * @param dto 查询条件
     * @return 总充值金额
     */
    BigDecimal getTotalRechargeAmount(@Param("dto") RechargeSummaryDto dto);

    /**
     * 查询本月充值金额
     * @param dto 查询条件
     * @return 本月充值金额
     */
    BigDecimal getCurrentMonthRechargeAmount(@Param("dto") RechargeSummaryDto dto);
}
