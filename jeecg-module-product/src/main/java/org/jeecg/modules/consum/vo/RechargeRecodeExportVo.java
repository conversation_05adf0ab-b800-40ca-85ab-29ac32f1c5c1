package org.jeecg.modules.consum.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 模拟充值记录导出VO
 * @Author: jeecg-boot
 * @Date: 2025-08-30
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class RechargeRecodeExportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "收款日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "收款日期")
    private Date orderDate;

    @Excel(name = "订单号", width = 20)
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @Excel(name = "收款金额", width = 15)
    @ApiModelProperty(value = "收款金额")
    private BigDecimal amount;

    @Excel(name = "客户收款", width = 15)
    @ApiModelProperty(value = "客户收款")
    private String customerPayment = "已收款";

    @Excel(name = "租户名称", width = 20)
    @ApiModelProperty(value = "租户名称")
    private String tenantName;
}
