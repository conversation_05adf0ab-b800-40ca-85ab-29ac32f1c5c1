package org.jeecg.modules.consum.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.consum.dto.RechargeRecodeDto;
import org.jeecg.modules.consum.dto.RechargeSummaryDto;
import org.jeecg.modules.consum.entity.RechargeRecode;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.consum.vo.RechargeRecodeVo;
import org.jeecg.modules.consum.vo.RechargeSummaryVo;

import java.util.List;

/**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
public interface IRechargeRecodeService extends IService<RechargeRecode> {

    IPage<RechargeRecodeVo> pageList(Page<RechargeRecodeVo> page, RechargeRecodeDto dto);

    /**
     * 根据ID列表获取记录
     * @param idList ID列表
     * @return 记录列表
     */
    List<RechargeRecodeVo> getListByIds(List<String> idList);

    /**
     * 获取充值汇总信息
     * @param dto 查询条件
     * @return 汇总信息
     */
    RechargeSummaryVo getRechargeSummary(RechargeSummaryDto dto);
}
