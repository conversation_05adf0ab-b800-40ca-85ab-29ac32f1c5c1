package org.jeecg.modules.consum.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 充值汇总查询DTO
 * @Author: jeecg-boot
 * @Date: 2025-08-30
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class RechargeSummaryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户id", example = "12345")
    private String tenantId;
}
