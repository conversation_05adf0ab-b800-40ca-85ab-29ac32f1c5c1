package org.jeecg.modules.consum.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.consum.dto.RechargeRecodeDto;
import org.jeecg.modules.consum.dto.RechargeSummaryDto;
import org.jeecg.modules.consum.entity.RechargeRecode;
import org.jeecg.modules.consum.mapper.RechargeRecodeMapper;
import org.jeecg.modules.consum.mapper.RechargeRecodeDetMapper;
import org.jeecg.modules.consum.service.IRechargeRecodeService;
import org.jeecg.modules.consum.vo.RechargeRecodeVo;
import org.jeecg.modules.consum.vo.RechargeSummaryVo;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Service
public class RechargeRecodeServiceImpl extends ServiceImpl<RechargeRecodeMapper, RechargeRecode> implements IRechargeRecodeService {

    @Autowired
    private RechargeRecodeDetMapper rechargeRecodeDetMapper;

    @Autowired
    private IClickReportHourlyService clickReportHourlyService;

    @Override
    public IPage<RechargeRecodeVo> pageList(Page<RechargeRecodeVo> page, RechargeRecodeDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }

        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }

        return this.baseMapper.pageList(page, dto);
    }

    @Override
    public List<RechargeRecodeVo> getListByIds(List<String> idList) {
        return this.baseMapper.getListByIds(idList);
    }

    @Override
    public RechargeSummaryVo getRechargeSummary(RechargeSummaryDto dto) {
        RechargeSummaryVo summaryVo = new RechargeSummaryVo();

        // 处理租户ID，如果为空或"0"则设置为null以查询所有租户
        RechargeSummaryDto queryDto = new RechargeSummaryDto();
        if (dto.getTenantId() != null && !dto.getTenantId().trim().isEmpty() && !"0".equals(dto.getTenantId().trim())) {
            queryDto.setTenantId(dto.getTenantId());
        } else {
            queryDto.setTenantId(null); // 设置为null以查询所有租户
        }

        // 查询总充值金额
        BigDecimal totalRechargeAmount = this.baseMapper.getTotalRechargeAmount(queryDto);
        summaryVo.setTotalRechargeAmount(totalRechargeAmount != null ? totalRechargeAmount : BigDecimal.ZERO);

        // 查询本月充值金额
        BigDecimal currentMonthRechargeAmount = this.baseMapper.getCurrentMonthRechargeAmount(queryDto);
        summaryVo.setCurrentMonthRechargeAmount(currentMonthRechargeAmount != null ? currentMonthRechargeAmount : BigDecimal.ZERO);

        // 查询本月消耗金额
        BigDecimal currentMonthConsumeAmount = rechargeRecodeDetMapper.getCurrentMonthConsumeAmount(queryDto);
        summaryVo.setCurrentMonthConsumeAmount(currentMonthConsumeAmount != null ? currentMonthConsumeAmount : BigDecimal.ZERO);

        // 查询今日点击数
        try {
            // 获取租户ID，如果为空或"0"则查询所有租户
            Integer tenantId = 0; // 默认查询所有租户
            if (dto.getTenantId() != null && !dto.getTenantId().trim().isEmpty() && !"0".equals(dto.getTenantId().trim())) {
                tenantId = Integer.parseInt(dto.getTenantId().trim());
            }
            Integer todayClickCount = clickReportHourlyService.getTodayClickCount(tenantId);
            summaryVo.setTodayClickCount(todayClickCount != null ? todayClickCount.longValue() : 0L);
        } catch (Exception e) {
            // 如果获取今日点击数失败，设置为0
            summaryVo.setTodayClickCount(0L);
        }

        return summaryVo;
    }
}
