package org.jeecg.modules.consum.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class RechargeRecodeDetDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询开始日期", example = "2023-01-01")
    private String startDate;

    @ApiModelProperty(value = "查询结束日期", example = "2023-12-31")
    private String endDate;

    @ApiModelProperty(value = "查询日期")
    private String dateRange;

    @ApiModelProperty(value = "租户id", example = "12345")
    private String tenantId;

    @ApiModelProperty(value = "多租户id集合")
    private List<String> tenantIds;

    @ApiModelProperty(value = "查询关键字keyword")
    private String keyword;

    @ApiModelProperty(value = "排序")
    private String orderBy;

    @ApiModelProperty(value = "选中ids")
    private String selections;

    @ApiModelProperty(value = "点击数开始", example = "100")
    private String clickNumStart;

    @ApiModelProperty(value = "点击数结束", example = "1000")
    private String clickNumEnd;

    @ApiModelProperty(value = "消耗金额开始", example = "10.00")
    private String amountStart;

    @ApiModelProperty(value = "消耗金额结束", example = "100.00")
    private String amountEnd;

    @ApiModelProperty(value = "是否可查看当前时间之后数据 0-不可查看 1-可查看")
    private Integer canViewFutureData;
}
