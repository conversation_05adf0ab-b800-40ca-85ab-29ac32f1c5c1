# 认证功能部署说明

## 快速部署步骤

### 1. 邮件配置

编辑 `jeecg-module-system/jeecg-system-start/src/main/resources/application-mail.yml` 文件：

```yaml
spring:
  mail:
    host: smtp.qq.com  # 根据你的邮件服务商修改
    port: 587
    username: <EMAIL>  # 你的邮箱
    password: your-password      # 你的邮箱授权码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 2. 常见邮件服务商配置

#### QQ邮箱
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-authorization-code  # QQ邮箱授权码，不是QQ密码
```

#### 163邮箱
```yaml
spring:
  mail:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: your-authorization-code  # 163邮箱授权码
```

#### Gmail
```yaml
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password  # Gmail应用专用密码
```

### 3. 获取邮箱授权码

#### QQ邮箱
1. 登录QQ邮箱网页版
2. 设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
3. 开启POP3/SMTP服务
4. 生成授权码

#### 163邮箱
1. 登录163邮箱网页版
2. 设置 → POP3/SMTP/IMAP
3. 开启POP3/SMTP服务
4. 设置客户端授权密码

#### Gmail
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用专用密码作为password

### 4. 启动应用

确保Redis已启动，然后启动Spring Boot应用：

```bash
cd jeecg-module-system/jeecg-system-start
mvn spring-boot:run
```

或者使用IDE运行 `JeecgSystemApplication.java`

### 5. 测试功能

访问测试页面：
```
http://localhost:8080/jeecg-boot/test-auth.html
```

或者在你的前端页面中测试登录弹框功能。

## 故障排除

### 邮件发送失败
1. 检查邮箱配置是否正确
2. 确认授权码是否有效
3. 检查网络连接
4. 查看应用日志

### Redis连接失败
1. 确认Redis服务已启动
2. 检查Redis连接配置
3. 验证Redis密码

### 接口调用失败
1. 检查接口路径是否正确
2. 确认请求参数格式
3. 查看后端日志

## API测试示例

### 登录接口
```bash
curl -X POST http://localhost:8080/jeecg-boot/sys/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

### 发送注册验证码
```bash
curl -X POST http://localhost:8080/jeecg-boot/sys/user/sendRegisterCode \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### 注册接口
```bash
curl -X POST http://localhost:8080/jeecg-boot/sys/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "code": "123456",
    "password": "newpassword"
  }'
```

## 注意事项

1. **生产环境**：请修改默认的邮箱配置
2. **安全性**：不要在代码中硬编码邮箱密码
3. **限流**：建议对验证码发送接口添加限流
4. **监控**：建议添加邮件发送成功率监控
5. **备份**：重要配置文件请做好备份

## 联系支持

如果遇到问题，请检查：
1. 应用日志
2. 邮件服务商文档
3. Redis连接状态
4. 网络连接情况
