package org.jeecg.config.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.TokenUtils;
import org.springframework.stereotype.Component;

/**
 * 租户工具类
 * 用于获取当前请求的租户ID
 * @author: jeecg-boot
 */
@Slf4j
@Component
public class TenantUtils {

    /**
     * 获取当前请求的租户ID
     * 直接从请求头中获取租户ID
     * 
     * @return 租户ID，如果获取失败返回null
     */
    public static String getCurrentTenantId() {
        try {
            String tenantId = TokenUtils.getTenantIdByRequest(SpringContextUtils.getHttpServletRequest());
            log.debug("获取到的租户ID: {}", tenantId);
            return tenantId;
        } catch (Exception e) {
            log.warn("获取租户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前请求的租户ID（带默认值）
     * 
     * @param defaultValue 默认值
     * @return 租户ID，如果获取失败返回默认值
     */
    public static String getCurrentTenantId(String defaultValue) {
        String tenantId = getCurrentTenantId();
        return tenantId != null ? tenantId : defaultValue;
    }

    /**
     * 检查当前租户是否为超级管理员
     * 超级管理员租户ID为"0"或null
     * 
     * @return true-是超级管理员，false-不是
     */
    public static boolean isSuperAdmin() {
        String tenantId = getCurrentTenantId();
        return tenantId == null || "0".equals(tenantId);
    }
}
