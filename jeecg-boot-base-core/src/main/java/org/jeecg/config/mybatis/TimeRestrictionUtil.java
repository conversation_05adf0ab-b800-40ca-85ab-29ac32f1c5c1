package org.jeecg.config.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间限制工具类
 * 用于处理查询时间限制，防止查询当前时间之后的数据
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
public class TimeRestrictionUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 检查租户是否可以查看当前时间之后的数据
     * 
     * @param tenantId 租户ID，如果为0或null表示超级管理员
     * @return 0-不可查看当前时间之后数据，1-可查看当前时间之后数据
     */
    public Integer checkCanViewFutureData(String tenantId) {
        // 如果租户ID为0或null，表示超级管理员，可以查看所有数据
        if (tenantId == null || "0".equals(tenantId.trim()) || tenantId.trim().isEmpty()) {
            log.debug("租户ID为0或空，允许查看当前时间之后数据");
            return 1;
        }
        
        // 其他租户不能查看当前时间之后的数据
        log.debug("租户ID为{}，不允许查看当前时间之后数据", tenantId);
        return 0;
    }

    /**
     * 处理查询结束时间，确保不超过当前时间
     * 
     * @param endDate 原始结束时间
     * @param canViewFutureData 是否可以查看未来数据 0-不可以 1-可以
     * @return 处理后的结束时间
     */
    public String processEndDate(String endDate, Integer canViewFutureData) {
        // 如果可以查看未来数据，直接返回原始结束时间
        if (canViewFutureData != null && canViewFutureData == 1) {
            log.debug("允许查看未来数据，返回原始结束时间: {}", endDate);
            return endDate;
        }

        String currentDate = getCurrentDate();
        
        // 如果没有指定结束时间，使用当前日期
        if (endDate == null || endDate.trim().isEmpty()) {
            log.debug("未指定结束时间，使用当前日期: {}", currentDate);
            return currentDate;
        }

        // 如果结束时间超过当前日期，限制为当前日期
        if (endDate.compareTo(currentDate) > 0) {
            log.debug("结束时间{}超过当前日期{}，限制为当前日期", endDate, currentDate);
            return currentDate;
        }

        return endDate;
    }

    /**
     * 处理查询开始时间
     * 
     * @param startDate 原始开始时间
     * @param canViewFutureData 是否可以查看未来数据 0-不可以 1-可以
     * @return 处理后的开始时间
     */
    public String processStartDate(String startDate, Integer canViewFutureData) {
        // 如果可以查看未来数据，直接返回原始开始时间
        if (canViewFutureData != null && canViewFutureData == 1) {
            log.debug("允许查看未来数据，返回原始开始时间: {}", startDate);
            return startDate;
        }

        // 如果没有指定开始时间，直接返回
        if (startDate == null || startDate.trim().isEmpty()) {
            return startDate;
        }

        String currentDate = getCurrentDate();
        
        // 如果开始时间超过当前日期，返回null（表示无有效数据）
        if (startDate.compareTo(currentDate) > 0) {
            log.debug("开始时间{}超过当前日期{}，返回null", startDate, currentDate);
            return null;
        }

        return startDate;
    }

    /**
     * 获取当前日期字符串
     * 
     * @return 当前日期 yyyy-MM-dd 格式
     */
    public String getCurrentDate() {
        return LocalDateTime.now().format(DATE_FORMATTER);
    }

    /**
     * 获取当前日期时间字符串
     * 
     * @return 当前日期时间 yyyy-MM-dd HH:mm:ss 格式
     */
    public String getCurrentDateTime() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }
}
