package org.jeecg.modules.demo.firmInformation.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmClickDto;
import org.jeecg.modules.demo.firmInformation.dto.WhFirmInformationDto;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.mapper.WhFirmInformationMapper;
import org.jeecg.modules.demo.firmInformation.service.IWhFirmInformationService;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickIpVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo;
import org.jeecg.modules.demo.firmInformation.vo.WhFirmInformationVo;
import org.jeecg.modules.demo.firmInformation.vo.WhPotalClickIpVo;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.Date;
import java.util.Calendar;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.config.mybatis.TimeRestrictionUtil;
import org.jeecg.config.mybatis.TenantUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * @Description: 合作公司
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Service
public class WhFirmInformationServiceImpl extends ServiceImpl<WhFirmInformationMapper, WhFirmInformation> implements IWhFirmInformationService {
    @Resource
    private IClickReportService clickReportService;
    @Autowired
    private TimeRestrictionUtil timeRestrictionUtil;

    @Override
    public IPage<WhFirmInformationVo> getCount(Page<WhFirmInformationVo> page, WhFirmInformationDto dto) {
        // 执行优化后的查询
        long startTime = System.currentTimeMillis();

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantUtils.getCurrentTenantId());

        // 设置默认时间范围，如果没有指定时间范围，默认查询最近30天
        if (dto.getStartTime() == null && dto.getEndTime() == null) {
            Calendar calendar = Calendar.getInstance();
            dto.setEndTime(calendar.getTime()); // 当前时间作为结束时间

            calendar.add(Calendar.DAY_OF_MONTH, -30); // 30天前作为开始时间
            dto.setStartTime(calendar.getTime());
        }

        // 如果不能查看未来数据，限制结束时间不超过当前时间
        if (canViewFutureData != null && canViewFutureData == 0) {
            Calendar currentCalendar = Calendar.getInstance();
            Date currentDate = currentCalendar.getTime();

            // 如果结束时间超过当前时间，则限制为当前时间
            if (dto.getEndTime() != null && dto.getEndTime().after(currentDate)) {
                dto.setEndTime(currentDate);
            }
        }

        List<WhFirmInformationVo> countList = this.baseMapper.getCount(page, dto);
        long endTime = System.currentTimeMillis();

        // 对 countList 进行添加序号 index
        for (int i = 0; i < countList.size(); i++) {
            countList.get(i).setIndex(i + 1);
        }
        page.setRecords(countList);
        return page;
    }

    @Override
    public List<WhFirmClickVo> getCountOus(WhFirmClickDto dto) {
        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantUtils.getCurrentTenantId());

        if ("month".equals(dto.getQueryType())) {
            try {
                // 格式如 2025-02
                String startStr = dto.getStartTime();
                String endStr = dto.getEndTime();

                // 解析为 YearMonth
                YearMonth startMonth = YearMonth.parse(startStr);
                YearMonth endMonth = YearMonth.parse(endStr);

                // 格式化为 yyyy-MM-dd
                LocalDate startDate = startMonth.atDay(1); // 月初
                LocalDate endDate = endMonth.atEndOfMonth(); // 月末

                // 如果不能查看未来数据，限制结束时间不超过当前时间
                if (canViewFutureData != null && canViewFutureData == 0) {
                    LocalDate currentDate = LocalDate.now();
                    if (endDate.isAfter(currentDate)) {
                        endDate = currentDate;
                    }
                }

                // 更新 dto
                dto.setStartTime(startDate.toString()); // 例如 2025-02-01
                dto.setEndTime(endDate.toString());     // 例如 2025-02-28
                return this.baseMapper.getCountOusBymonth(dto);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("startTime 格式错误，应为 yyyy-MM", e);
            }
        }

        // 对于非月份查询，也需要进行时间限制
        if (canViewFutureData != null && canViewFutureData == 0) {
            String currentDate = LocalDate.now().toString();
            if (dto.getEndTime() != null && dto.getEndTime().compareTo(currentDate) > 0) {
                dto.setEndTime(currentDate);
            }
        }

        return this.baseMapper.getCountOus(dto);
    }

    @Override
    public WhPotalClickIpVo getIpCount(WhFirmClickDto dto) {
        WhPotalClickIpVo ipVo = new WhPotalClickIpVo();

        // 检查是否可以查看当前时间之后的数据
        Integer canViewFutureData = timeRestrictionUtil.checkCanViewFutureData(TenantUtils.getCurrentTenantId());

        // ===== 处理 queryType 为 month 的时间范围 =====
        if ("month".equals(dto.getQueryType())) {
            try {
                YearMonth startMonth = YearMonth.parse(dto.getStartTime()); // 例如 "2025-03"
                YearMonth endMonth = YearMonth.parse(dto.getEndTime());

                LocalDate startDate = startMonth.atDay(1);
                LocalDate endDate = endMonth.atEndOfMonth();

                // 如果不能查看未来数据，限制结束时间不超过当前时间
                if (canViewFutureData != null && canViewFutureData == 0) {
                    LocalDate currentDate = LocalDate.now();
                    if (endDate.isAfter(currentDate)) {
                        endDate = currentDate;
                    }
                }

                dto.setStartTime(startDate.toString());  // 2025-03-01
                dto.setEndTime(endDate.toString());      // 2025-03-31
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("startTime 格式错误，应为 yyyy-MM", e);
            }
        } else {
            // 对于非月份查询，也需要进行时间限制
            if (canViewFutureData != null && canViewFutureData == 0) {
                String currentDate = LocalDate.now().toString();
                if (dto.getEndTime() != null && dto.getEndTime().compareTo(currentDate) > 0) {
                    dto.setEndTime(currentDate);
                }
            }
        }

        // ===== 查询 IP 报表列表 =====
        List<WhFirmClickIpVo> whPotalClickIpVoss = this.baseMapper.getIpCount(dto);
        ipVo.setWhPotalClickIpVos(whPotalClickIpVoss);

        // ===== 计算总访问量 =====
        int totalIp = whPotalClickIpVoss.stream().mapToInt(WhFirmClickIpVo::getIpSum).sum();
        ipVo.setCountIp(String.valueOf(totalIp));

        // ===== 查询当天点击数和模拟 IP =====
        if ("day".equals(dto.getQueryType())) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startOfDay = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endOfDay = calendar.getTime();

            ClickReport clickReport = clickReportService.lambdaQuery()
                    .eq(ClickReport::getCompanyId, dto.getId())
                    .eq(ClickReport::getStatDate, startOfDay)
                    .one();

            if (ObjUtil.isNotEmpty(clickReport)) {
                double baseValue = clickReport.getClickNum();
                double randomPercentage = 103 + (108 - 103) * new Random().nextDouble();
                int finalValue = (int) Math.round(baseValue * randomPercentage / 100.0);

                ipVo.setDayIp(String.valueOf(finalValue));
                ipVo.setDayClick(String.valueOf(clickReport.getClickNum()));
            }
        }

        return ipVo;
    }
}
