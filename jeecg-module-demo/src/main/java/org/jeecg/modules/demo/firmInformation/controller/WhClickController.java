package org.jeecg.modules.demo.firmInformation.controller;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.demo.firmInformation.dto.ExportXlsClickDto;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 点击数
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Api(tags="点击数")
@RestController
@RequestMapping("/firmInformation/whClick")
@Slf4j
public class WhClickController extends JeecgController<WhClick, IWhClickService> {
	@Autowired
	private IWhClickService whClickService;

	 /**
	 * 分页列表查询
	 *
	 * @param whClick
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "点击数-分页列表查询")
	@ApiOperation(value="点击数-分页列表查询", notes="点击数-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WhClick>> queryPageList(WhClick whClick,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WhClick> queryWrapper = QueryGenerator.initQueryWrapper(whClick, req.getParameterMap());
		Page<WhClick> page = new Page<WhClick>(pageNo, pageSize);
		IPage<WhClick> pageList = whClickService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param whClick
	 * @return
	 */
	@AutoLog(value = "点击数-添加")
	@ApiOperation(value="点击数-添加", notes="点击数-添加")
	@RequiresPermissions("firmInformation:wh_click:add")
	@PostMapping(value = "/add")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> add(@RequestBody WhClick whClick) {
		whClickService.save(whClick);
		return Result.OK("添加成功！");
	}

	 /**
	  *   添加
	  *
	  * @return
	  */
	 @AutoLog(value = "点击数-加1")
	 @ApiOperation(value="点击数-加1", notes="点击数-加1")
	 @GetMapping(value = "/addOne")
	 @CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	 public Result<String> addOne(@RequestParam String clicksId) {
		 WhClick whClick = whClickService.getById(clicksId);
		 if (Objects.isNull(whClick)) {
			 whClick = whClickService.lambdaQuery().eq(WhClick::getPid, clicksId).one();
		 }
		 whClickService.addNum(whClick.getId());
		 return Result.OK("添加成功！");
	 }


	 /**
	 *  编辑
	 *
	 * @param whClick
	 * @return
	 */
	@AutoLog(value = "点击数-编辑")
	@ApiOperation(value="点击数-编辑", notes="点击数-编辑")
	@RequiresPermissions("firmInformation:wh_click:edit")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WhClick whClick) {
		whClickService.updateById(whClick);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "点击数-通过id删除")
	@ApiOperation(value="点击数-通过id删除", notes="点击数-通过id删除")
	@RequiresPermissions("firmInformation:wh_click:delete")
	@DeleteMapping(value = "/delete")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		whClickService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "点击数-批量删除")
	@ApiOperation(value="点击数-批量删除", notes="点击数-批量删除")
	@RequiresPermissions("firmInformation:wh_click:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	@CacheEvict(value = "WhFirmInformation", key = "'wh_firmInformation_list'")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.whClickService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "点击数-通过id查询")
	@ApiOperation(value="点击数-通过id查询", notes="点击数-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WhClick> queryById(@RequestParam(name="id",required=true) String id) {
		WhClick whClick = whClickService.getById(id);
		if(whClick==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(whClick);
	}

    /**
    * 导出excel
    *
    * @param request
    */
    @RequestMapping(value = "/exportXls")
	public void exportXls(HttpServletRequest request,@RequestBody ExportXlsClickDto dto, HttpServletResponse response) throws Exception {
		List<Map<String, Object>> mapList = new ArrayList<>();
		Random random = new Random();

		String[] provinces = {
				"北京", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江",
				"上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南",
				"湖北", "湖南", "广东", "广西", "海南", "重庆", "四川", "贵州",
				"云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"
		};

		int totalClicks = 0;
		int id = 0;

		// 解析日期范围
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date startDate = sdf.parse(dto.getStartTime());
		Date endDate = sdf.parse(dto.getEndTime());
		long dateRange = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24) + 1;

		List<String> dateList = new ArrayList<>();
		for (int i = 0; i < dateRange; i++) {
			dateList.add(sdf.format(new Date(startDate.getTime() + (i * 86400000L))));
		}

		List<String> assignedDates = new ArrayList<>();

		if (dto.getNnum() >= dateRange) {
			// **nnum > 日期范围，确保所有日期都有数据**
			while (assignedDates.size() < dto.getNnum()) {
				assignedDates.addAll(dateList);
			}
			assignedDates = assignedDates.subList(0, dto.getNnum());
		} else {
			// **nnum ≤ 日期范围，随机选取日期**
			Collections.shuffle(dateList);
			assignedDates = dateList.subList(0, dto.getNnum());
		}

		if (dto.getGenerateType() == 0) {
			// **模式 0：固定点击数，每条 num=1**
			for (int j = 0; j < dto.getNnum(); j++) {
				Map<String, Object> lm = new HashMap<>();
				String selectedProvince = provinces[random.nextInt(provinces.length)];
				String ip = ProvinceIpGenerator.generateIpByProvince(selectedProvince, random);

				lm.put("id", ++id);
				lm.put("ip", ip);
				lm.put("address", selectedProvince);
				lm.put("date", assignedDates.get(j));
				lm.put("time", generateRandomTime(random));
				lm.put("num", 1);

				totalClicks++;
				mapList.add(lm);
			}
		} else if (dto.getGenerateType() == 1) {
			// **模式 1：随机点击数，总和等于 clicksnum**
			List<Integer> clickDistribution = new ArrayList<>(Collections.nCopies(dto.getClicksnum(), 1));
			int remainingClicks = dto.getClicksnum() - dto.getNnum();

			if (dto.getNnum() <= dto.getClicksnum()) {
				// **先分配基础点击数**
				for (int j = 0; j < dto.getNnum(); j++) {
					clickDistribution.set(j, 1);
				}

				// **然后随机分配剩余点击数，采用加权分布**
				while (remainingClicks > 0) {
					for (int j = 0; j < dto.getNnum() && remainingClicks > 0; j++) {
						// **随机决定是否增加**
						if (random.nextDouble() < 0.7) { // **70% 的概率增加**
							int maxAssignable = Math.min(9, remainingClicks);
							int num = random.nextInt(maxAssignable) + 1; // **1~maxAssignable**
							clickDistribution.set(j, clickDistribution.get(j) + num);
							remainingClicks -= num;
						}
					}
				}
			} else {
				// **nnum > clicksnum，部分数据 num 置空**
				remainingClicks = dto.getClicksnum();

				// **1. 先给前 clicksnum 个条目分配 1**
				for (int j = 0; j < dto.getClicksnum(); j++) {
					clickDistribution.set(j, 1);
				}

				// **2. 其余数据 num 置空**
				for (int j = dto.getClicksnum(); j < dto.getNnum(); j++) {
					clickDistribution.set(j, null);
				}
			}

			// **生成最终数据**
			for (int j = 0; j < dto.getNnum(); j++) {
				Map<String, Object> lm = new HashMap<>();
				String selectedProvince = provinces[random.nextInt(provinces.length)];
				String ip = ProvinceIpGenerator.generateIpByProvince(selectedProvince, random);

				lm.put("id", ++id);
				lm.put("ip", ip);
				lm.put("address", selectedProvince);
				lm.put("date", assignedDates.get(j));
				lm.put("time", generateRandomTime(random));

				// **nnum > clicksnum 时，部分数据 num 为空**
				Integer num = clickDistribution.get(j);
				lm.put("num", num != null ? num : "");

				if (num != null) {
					totalClicks += num;
				}
				mapList.add(lm);
			}
		}

		// 组装导出数据
		Map<String, Object> map = new HashMap<>();
		map.put("maplist", mapList);
		map.put("sum", totalClicks);

		// 调用模板导出
		myTemplateExport(map, "taiyiclick.xls", "网页点击量", request, response);
	}


	 private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	 /**
	  * 获取起始日期和结束日期之间的所有日期列表
	  */
	 public  List<String> getDateRange(String startDateStr, String endDateStr) {
		 List<String> dateList = new ArrayList<>();
		 try {
			 LocalDate startDate = LocalDate.parse(startDateStr, FORMATTER);
			 LocalDate endDate = LocalDate.parse(endDateStr, FORMATTER);

			 if (startDate.isAfter(endDate)) {
				 return dateList; // 返回空列表，表示无效范围
			 }

			 LocalDate currentDate = startDate;
			 while (!currentDate.isAfter(endDate)) {
				 dateList.add(currentDate.format(FORMATTER));
				 currentDate = currentDate.plusDays(1);
			 }
		 } catch (DateTimeParseException e) {
			 System.err.println("日期解析异常: " + e.getMessage());
		 }
		 return dateList;
	 }

	 private String generateRandomTime(Random random) {
		 // 假设的随机时间生成方法
		 int hour = random.nextInt(24);
		 int minute = random.nextInt(60);
		 int second = random.nextInt(60);
		 return String.format("%02d:%02d:%02d", hour, minute, second);
	 }






	 private String generateRandomIp() {
		 Random random = new Random();
		 return random.nextInt(256) + "." + random.nextInt(256) + "." + random.nextInt(256) + "." + random.nextInt(256);
	 }

	 /**
	  * 自定义excel导出方法
	  * @param dataMap 数据
	  * @param templateName 模板文件
	  * @param fileName 文件名称
	  * @param request 请求
	  * @param response
	  * @throws Exception
	  */
	 protected void myTemplateExport(Map<String, Object> dataMap, String templateName, String fileName, HttpServletRequest request, HttpServletResponse response) throws Exception {
		 // 表头构造器列表（未使用时可以保留为空）
		 List<ExcelExportEntity> entityList = new ArrayList<>();
		 String codedFileName = "临时文件";
		 Workbook workbook = null;

		 // 设置默认文件名
		 if (StrUtil.isNotBlank(fileName)) {
			 codedFileName = fileName;
		 }

		 // 从类路径加载模板文件为输入流
		 ClassPathResource resource = new ClassPathResource("excel/" + templateName);
		 File tempFile = null;
		 try (InputStream inputStream = resource.getInputStream()) {
			 // 创建临时文件
			 tempFile = File.createTempFile("excel_template_", ".xls");
			 FileUtils.copyInputStreamToFile(inputStream, tempFile);

			 // 使用临时文件路径创建 TemplateExportParams
			 TemplateExportParams params = new TemplateExportParams(tempFile.getAbsolutePath());
			 workbook = ExcelExportUtil.exportExcel(params, dataMap);

			 // 根据工作簿类型设置文件扩展名
			 if (workbook instanceof HSSFWorkbook) {
				 codedFileName = codedFileName + ".xls";
			 } else {
				 codedFileName = codedFileName + ".xlsx";
			 }

			 // 设置响应头，支持中文文件名
			 codedFileName = new String(codedFileName.getBytes("UTF-8"), "ISO-8859-1");
			 response.setHeader("content-disposition", "attachment;filename=" + codedFileName);

			 // 输出工作簿到响应流
			 ServletOutputStream out = response.getOutputStream();
			 workbook.write(out);
			 out.flush();
		 } catch (Exception e) {
			 throw new Exception("导出 Excel 文件失败: " + e.getMessage(), e);
		 } finally {
			 // 清理临时文件
			 if (tempFile != null && tempFile.exists()) {
				 tempFile.delete();
			 }
			 // 关闭工作簿资源
			 if (workbook != null) {
				 try {
					 workbook.close();
				 } catch (IOException e) {
					 // 记录日志或忽略
				 }
			 }
		 }
	 }


    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("firmInformation:wh_click:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WhClick.class);
    }

}
