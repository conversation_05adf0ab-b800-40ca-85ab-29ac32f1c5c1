package org.jeecg.modules.demo.firmInformation.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.entity.WhFirmInformation;
import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.demo.firmInformation.service.IWhNewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags="首页新闻资讯")
@RestController
@RequestMapping("/api/firmInformation/whNews")
@Slf4j
public class ApiWhNewsController {
    @Autowired
    private IWhNewsService whNewsService;
    @Autowired
    private IWhClickService whClickService;

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "新闻资讯-分页列表查询")
    @ApiOperation(value="新闻资讯-分页列表查询", notes="新闻资讯-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(@RequestParam(name="name", required = false) String name,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="15") Integer pageSize,
                                   HttpServletRequest req) {
        // 使用优化后的Service方法，在数据库层面完成JOIN查询和日期格式化
        Page<WhNews> page = new Page<>(pageNo, pageSize);
        IPage<WhNews> pageList = whNewsService.queryPageListOptimized(page, name);
        return Result.OK(pageList);
    }

    /**
     * 通过id查询（优化版本）
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "wh_news-通过id查询")
    @ApiOperation(value="wh_news-通过id查询", notes="wh_news-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WhNews> queryById(@RequestParam(name="id",required=true) String id) {
        // 使用优化后的Service方法，在数据库层面完成JOIN查询和日期格式化
        WhNews whNews = whNewsService.getByIdOptimized(id);
        if(whNews == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(whNews);
    }
}
