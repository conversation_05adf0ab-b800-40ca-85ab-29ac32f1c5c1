package org.jeecg.modules.demo.firmInformation.service;

import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: wh_news
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
public interface IWhNewsService extends IService<WhNews> {

    /**
     * 分页查询新闻资讯列表（优化版本）
     * @param page 分页参数
     * @param name 新闻标题（可选）
     * @return 分页结果
     */
    IPage<WhNews> queryPageListOptimized(Page<WhNews> page, String name);

    /**
     * 批量更新其他记录为非置顶（优化版本）
     * @param excludeId 排除的记录ID（可选）
     */
    void updateOthersToNotTop(String excludeId);

    /**
     * 根据ID查询新闻详情（优化版本）
     * @param id 新闻ID
     * @return 新闻详情
     */
    WhNews getByIdOptimized(String id);

}
