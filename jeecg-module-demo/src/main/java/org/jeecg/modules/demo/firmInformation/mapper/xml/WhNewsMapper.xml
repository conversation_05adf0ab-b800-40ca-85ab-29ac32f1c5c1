<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.firmInformation.mapper.WhNewsMapper">

    <!-- 优化的分页查询新闻资讯列表 -->
    <select id="queryPageListOptimized" resultType="org.jeecg.modules.demo.firmInformation.entity.WhNews">
        SELECT
            n.id,
            n.message_time,
            n.news_img,
            n.clicks_id,
            n.name,
            DATE_FORMAT(n.message_time, '%Y/%c/%e') as message_timing,
            COALESCE(c.wh_num, 0) as clicks_num
        FROM wh_news n
        LEFT JOIN wh_click c ON n.clicks_id = c.id
        WHERE n.is_delete = 0
        AND n.enable_type = 'Y'
        <if test="name != null and name != ''">
            AND n.name LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY n.create_time DESC
    </select>

    <!-- 批量更新其他记录为非置顶 -->
    <update id="updateOthersToNotTop">
        UPDATE wh_news
        SET top_type = 'N'
        WHERE is_delete = 0
        AND top_type = 'Y'
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </update>

    <!-- 根据ID查询新闻详情（优化版本） -->
    <select id="getByIdOptimized" resultType="org.jeecg.modules.demo.firmInformation.entity.WhNews">
        SELECT
            n.*,
            DATE_FORMAT(n.message_time, '%Y/%c/%e') as message_timing,
            COALESCE(c.wh_num, 0) as clicks_num
        FROM wh_news n
        LEFT JOIN wh_click c ON n.clicks_id = c.id
        WHERE n.id = #{id}
    </select>

</mapper>