package org.jeecg.modules.demo.firmInformation.service.impl;

import org.jeecg.modules.demo.firmInformation.entity.WhNews;
import org.jeecg.modules.demo.firmInformation.mapper.WhNewsMapper;
import org.jeecg.modules.demo.firmInformation.service.IWhNewsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: wh_news
 * @Author: jeecg-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Service
public class WhNewsServiceImpl extends ServiceImpl<WhNewsMapper, WhNews> implements IWhNewsService {

    @Override
    public IPage<WhNews> queryPageListOptimized(Page<WhNews> page, String name) {
        return this.baseMapper.queryPageListOptimized(page, name);
    }

    @Override
    public void updateOthersToNotTop(String excludeId) {
        this.baseMapper.updateOthersToNotTop(excludeId);
    }

    @Override
    public WhNews getByIdOptimized(String id) {
        return this.baseMapper.getByIdOptimized(id);
    }

}
